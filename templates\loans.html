{% extends "base.html" %}

{% block title %}借用管理 - 手机管理系统{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-5">
    <div>
        <h1 class="h2 mb-1 fw-bold">借用管理</h1>
        <p class="text-muted mb-0">设备借用与归还流程</p>
    </div>
    <a href="{{ url_for('new_loan') }}" class="btn btn-primary">
        <i class="bi bi-plus me-2"></i>新建借用
    </a>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-5">
    <div class="col-md-6">
        <div class="stat-card bg-info bg-opacity-10">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-info bg-opacity-10">
                    <i class="bi bi-arrow-left-right text-info"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number text-info">{{ active_loans|length }}</div>
                    <div class="stat-label">活跃借用</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="stat-card bg-warning bg-opacity-10">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-warning bg-opacity-10">
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number text-warning">{{ overdue_loans|length }}</div>
                    <div class="stat-label">逾期借用</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 逾期借用提醒 -->
{% if overdue_loans %}
<div class="alert alert-warning border-0 mb-4">
    <div class="d-flex align-items-center">
        <i class="bi bi-exclamation-triangle-fill me-3 fs-4"></i>
        <div class="flex-grow-1">
            <h5 class="alert-heading mb-2">逾期借用提醒</h5>
            <p class="mb-3">以下 {{ overdue_loans|length }} 条借用记录已逾期，请及时处理：</p>
            <div class="table-responsive">
                <table class="table table-sm table-borderless mb-0">
                    <thead>
                        <tr class="text-muted">
                            <th class="fw-medium">记录ID</th>
                            <th class="fw-medium">IMEI</th>
                            <th class="fw-medium">手机型号</th>
                            <th class="fw-medium">借用者</th>
                            <th class="fw-medium">预期归还</th>
                            <th class="fw-medium">状态</th>
                            <th class="fw-medium text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for loan in overdue_loans %}
                            <tr class="border-0">
                                <td><span class="text-muted">{{ loan.id }}</span></td>
                                <td><code class="text-muted">{{ loan.phone_imei }}</code></td>
                                <td><span class="fw-medium">{{ loan.phone.brand }} {{ loan.phone.model }}</span></td>
                                <td><span class="fw-medium">{{ loan.borrower.name }}</span></td>
                                <td><span class="text-muted">{{ loan.expected_return_date|date }}</span></td>
                                <td><span class="badge bg-danger-subtle text-danger">逾期</span></td>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-success" 
                                            onclick="returnPhone({{ loan.id }}, '{{ loan.phone_imei }}')">
                                        <i class="bi bi-check-circle me-1"></i>归还
                                    </button>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 活跃借用记录 -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            活跃借用记录
            {% if active_loans %}
                <span class="badge bg-light text-dark ms-2">{{ active_loans|length }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body">
        {% if active_loans %}
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th class="fw-semibold">记录ID</th>
                            <th class="fw-semibold">IMEI</th>
                            <th class="fw-semibold">手机型号</th>
                            <th class="fw-semibold">借用者</th>
                            <th class="fw-semibold">出借者</th>
                            <th class="fw-semibold">借用日期</th>
                            <th class="fw-semibold">预期归还</th>
                            <th class="fw-semibold">借用目的</th>
                            <th class="fw-semibold text-center">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for loan in active_loans %}
                            <tr class="border-0">
                                <td><span class="text-muted">{{ loan.id }}</span></td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <code class="text-muted">{{ loan.phone_imei }}</code>
                                        <a href="{{ url_for('phone_detail', imei=loan.phone_imei) }}" 
                                           class="btn btn-xs btn-outline-primary" title="查看手机详情">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-medium">{{ loan.phone.brand }} {{ loan.phone.model }}</div>
                                        <small class="text-muted">{{ loan.phone.color }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-medium">{{ loan.borrower.name }}</div>
                                        <small class="text-muted">{{ loan.borrower.department }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-medium">{{ loan.lender.name }}</div>
                                        <small class="text-muted">{{ loan.lender.department }}</small>
                                    </div>
                                </td>
                                <td><span class="text-muted">{{ loan.loan_date|date }}</span></td>
                                <td>
                                    {% if loan.expected_return_date %}
                                        <span class="text-muted">{{ loan.expected_return_date|date }}</span>
                                    {% else %}
                                        <span class="text-muted">未设定</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if loan.purpose %}
                                        <span class="text-muted">{{ loan.purpose }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-success" 
                                            onclick="returnPhone({{ loan.id }}, '{{ loan.phone_imei }}')">
                                        <i class="bi bi-check-circle me-1"></i>归还
                                    </button>
                                </td>
                            </tr>
                        {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="bi bi-arrow-left-right text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mb-3">暂无活跃的借用记录</h5>
                            <p class="text-muted mb-4">点击上方按钮创建新的借用记录</p>
                            <a href="{{ url_for('new_loan') }}" class="btn btn-primary">
                                <i class="bi bi-plus me-2"></i>新建借用
                            </a>
                        </div>
                    </div>
                {% endif %}
    </div>
</div>

<!-- 归还确认模态框 -->
<div class="modal fade" id="returnModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认归还</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要归还手机 <strong id="returnPhoneImei"></strong> 吗？</p>
                <p class="text-info small">归还后，手机将回到出借者手中。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmReturn">确认归还</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let returnLoanId = null;

function returnPhone(loanId, phoneImei) {
    returnLoanId = loanId;
    document.getElementById('returnPhoneImei').textContent = phoneImei;
    const modal = new bootstrap.Modal(document.getElementById('returnModal'));
    modal.show();
}

document.getElementById('confirmReturn').addEventListener('click', function() {
    if (returnLoanId) {
        const submitBtn = this;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
        submitBtn.disabled = true;
        
        fetch(`/loans/${returnLoanId}/return`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', data.message);
            }
            bootstrap.Modal.getInstance(document.getElementById('returnModal')).hide();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '归还失败，请重试');
            bootstrap.Modal.getInstance(document.getElementById('returnModal')).hide();
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }
});
</script>
{% endblock %}
