{% extends "base.html" %}

{% block title %}用户管理 - 手机管理系统{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-5">
    <div>
        <h1 class="h2 mb-1 fw-bold">用户管理</h1>
        <p class="text-muted mb-0">团队成员与权限管理</p>
    </div>
    <a href="{{ url_for('add_user') }}" class="btn btn-primary">
        <i class="bi bi-plus me-2"></i>添加用户
    </a>
</div>

<!-- 用户列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">用户列表</h5>
        {% if users %}
            <span class="badge bg-light text-dark">{{ users|length }} 个用户</span>
        {% endif %}
    </div>
    <div class="card-body">
                <!-- 搜索和排序区域 -->
                <div class="row g-3 mb-4">
                    <div class="col-md-8">
                        <form method="GET" action="{{ url_for('users') }}" class="d-flex">
                            <div class="input-group">
                                <span class="input-group-text border-end-0 bg-light">
                                    <i class="bi bi-search text-muted"></i>
                                </span>
                                <input type="text" 
                                       class="form-control border-start-0" 
                                       name="keyword" 
                                       value="{{ search_params.keyword or '' }}"
                                       placeholder="搜索用户（姓名、部门、邮箱、电话）">
                                <input type="hidden" name="sort_by" value="{{ search_params.sort_by or 'id' }}">
                                <button class="btn btn-outline-primary" type="submit">
                                    搜索
                                </button>
                                {% if search_params.keyword %}
                                    <a href="{{ url_for('users') }}?sort_by={{ search_params.sort_by or 'id' }}" 
                                       class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle"></i>
                                    </a>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <form method="GET" action="{{ url_for('users') }}">
                            <input type="hidden" name="keyword" value="{{ search_params.keyword or '' }}">
                            <select class="form-select" name="sort_by" onchange="this.form.submit()">
                                <option value="id" {% if search_params.sort_by == 'id' %}selected{% endif %}>按ID排序</option>
                                <option value="name" {% if search_params.sort_by == 'name' %}selected{% endif %}>按姓名排序</option>
                                <option value="department" {% if search_params.sort_by == 'department' %}selected{% endif %}>按部门排序</option>
                                <option value="created_at" {% if search_params.sort_by == 'created_at' %}selected{% endif %}>按创建时间排序</option>
                            </select>
                        </form>
                    </div>
                </div>

                {% if users %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th class="fw-semibold">
                                        <a href="{{ url_for('users') }}?keyword={{ search_params.keyword or '' }}&sort_by=id" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            ID
                                            {% if search_params.sort_by == 'id' or not search_params.sort_by %}
                                                <i class="bi bi-sort-numeric-down text-primary ms-1"></i>
                                            {% endif %}
                                        </a>
                                    </th>
                                    <th class="fw-semibold">
                                        <a href="{{ url_for('users') }}?keyword={{ search_params.keyword or '' }}&sort_by=name" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            姓名
                                            {% if search_params.sort_by == 'name' %}
                                                <i class="bi bi-sort-alpha-down text-primary ms-1"></i>
                                            {% endif %}
                                        </a>
                                    </th>
                                    <th class="fw-semibold">
                                        <a href="{{ url_for('users') }}?keyword={{ search_params.keyword or '' }}&sort_by=department" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            部门
                                            {% if search_params.sort_by == 'department' %}
                                                <i class="bi bi-sort-alpha-down text-primary ms-1"></i>
                                            {% endif %}
                                        </a>
                                    </th>
                                    <th class="fw-semibold">邮箱</th>
                                    <th class="fw-semibold">电话</th>
                                    <th class="fw-semibold">
                                        <a href="{{ url_for('users') }}?keyword={{ search_params.keyword or '' }}&sort_by=created_at" 
                                           class="text-decoration-none text-dark d-flex align-items-center">
                                            创建时间
                                            {% if search_params.sort_by == 'created_at' %}
                                                <i class="bi bi-sort-numeric-down text-primary ms-1"></i>
                                            {% endif %}
                                        </a>
                                    </th>
                                    <th class="fw-semibold text-center">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                    <tr class="border-0">
                                        <td><span class="text-muted">{{ user.id }}</span></td>
                                        <td>
                                            <div class="fw-medium">{{ user.name }}</div>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">{{ user.department }}</span>
                                        </td>
                                        <td>
                                            <a href="mailto:{{ user.email }}" class="text-decoration-none">{{ user.email }}</a>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ user.phone_number or '-' }}</span>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '未设置' }}</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('user_detail', user_id=user.id) }}" 
                                                   class="btn btn-outline-primary" title="查看详情">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ url_for('edit_user', user_id=user.id) }}" 
                                                   class="btn btn-outline-warning" title="编辑">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-outline-danger" 
                                                        title="删除"
                                                        onclick="deleteUser({{ user.id }}, '{{ user.name }}')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="bi bi-people text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mb-3">暂无用户数据</h5>
                            <p class="text-muted mb-4">点击上方按钮添加第一个用户</p>
                            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                                <i class="bi bi-plus me-2"></i>添加用户
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                <p class="text-danger small">注意：如果用户有关联的手机，将无法删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let deleteUserId = null;

function deleteUser(userId, userName) {
    deleteUserId = userId;
    document.getElementById('deleteUserName').textContent = userName;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (deleteUserId) {
        fetch(`/users/${deleteUserId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', data.message);
            }
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '删除失败，请重试');
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        });
    }
});
</script>
{% endblock %}
