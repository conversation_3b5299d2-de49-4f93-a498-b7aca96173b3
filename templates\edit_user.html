{% extends "base.html" %}

{% block title %}编辑用户 - {{ user.name }} - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-pencil"></i> 编辑用户
            </h1>
            <div>
                <a href="{{ url_for('user_detail', user_id=user.id) }}" class="btn btn-info me-2">
                    <i class="bi bi-eye"></i> 查看详情
                </a>
                <a href="{{ url_for('users') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回用户列表
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">编辑用户信息</h5>
            </div>
            <div class="card-body">
                <form id="editUserForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            姓名 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="{{ user.name }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="department" class="form-label">
                            部门 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="department" name="department" 
                               value="{{ user.department }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            邮箱 <span class="text-danger">*</span>
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="{{ user.email }}" required>
                        <div class="form-text">请输入有效的邮箱地址</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone_number" class="form-label">电话号码</label>
                        <input type="tel" class="form-control" id="phone_number" name="phone_number" 
                               value="{{ user.phone_number or '' }}">
                        <div class="form-text">可选项</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <small>
                                <i class="bi bi-info-circle"></i> 
                                <strong>创建时间:</strong> {{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '未设置' }}
                            </small>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('user_detail', user_id=user.id) }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存更改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
    submitBtn.disabled = true;
    
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.href = '{{ url_for("user_detail", user_id=user.id) }}';
            }, 1500);
        } else {
            showAlert('danger', data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '保存失败，请重试');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
