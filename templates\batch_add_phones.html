{% extends "base.html" %}

{% block title %}批量添加手机 - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-phone-vibrate"></i> 批量添加手机
            </h1>
            <div>
                <a href="{{ url_for('add_phone') }}" class="btn btn-outline-primary me-2">
                    <i class="bi bi-plus"></i> 单个添加
                </a>
                <a href="{{ url_for('phones') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回手机列表
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">批量添加手机信息</h5>
            </div>
            <div class="card-body">
                <!-- 操作按钮 -->
                <div class="mb-3">
                    <button type="button" class="btn btn-success me-2" onclick="addPhoneRow()">
                        <i class="bi bi-plus-circle"></i> 添加一行
                    </button>
                    <button type="button" class="btn btn-warning me-2" onclick="clearAll()">
                        <i class="bi bi-trash"></i> 清空所有
                    </button>
                    <button type="button" class="btn btn-info me-2" onclick="fillSampleData()">
                        <i class="bi bi-clipboard-data"></i> 填充示例数据
                    </button>
                </div>

                <form id="batchAddForm">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="phonesTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="15%">IMEI编码 <span class="text-danger">*</span></th>
                                    <th width="10%">品牌 <span class="text-danger">*</span></th>
                                    <th width="12%">型号 <span class="text-danger">*</span></th>
                                    <th width="8%">颜色 <span class="text-danger">*</span></th>
                                    <th width="10%">项目 <span class="text-danger">*</span></th>
                                    <th width="15%">初始拥有者 <span class="text-danger">*</span></th>
                                    <th width="22%">备注</th>
                                    <th width="8%">操作</th>
                                </tr>
                            </thead>
                            <tbody id="phonesTableBody">
                                <!-- 初始行 -->
                                <tr>
                                    <td><input type="text" class="form-control form-control-sm" name="imei" required></td>
                                    <td><input type="text" class="form-control form-control-sm" name="brand" required></td>
                                    <td><input type="text" class="form-control form-control-sm" name="model" required></td>
                                    <td><input type="text" class="form-control form-control-sm" name="color" required></td>
                                    <td><input type="text" class="form-control form-control-sm" name="project" required></td>
                                    <td>
                                        <select class="form-select form-select-sm" name="original_owner_id" required>
                                            <option value="">选择拥有者</option>
                                            {% for user in users %}
                                                <option value="{{ user.id }}">{{ user.name }} ({{ user.department }})</option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td><input type="text" class="form-control form-control-sm" name="notes"></td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeRow(this)">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    {% if not users %}
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> 
                            暂无用户，请先 <a href="{{ url_for('add_user') }}">添加用户</a>
                        </div>
                    {% endif %}

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-3">
                        <a href="{{ url_for('phones') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary" {% if not users %}disabled{% endif %}>
                            <i class="bi bi-check-circle"></i> 批量添加手机
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 结果显示模态框 -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量添加结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="resultContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="goToPhoneList()">查看手机列表</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let rowCount = 1;

function addPhoneRow() {
    const tbody = document.getElementById('phonesTableBody');
    const newRow = tbody.rows[0].cloneNode(true);
    
    // 清空输入值
    const inputs = newRow.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (input.type === 'text') {
            input.value = '';
        } else if (input.tagName === 'SELECT') {
            input.selectedIndex = 0;
        }
    });
    
    tbody.appendChild(newRow);
    rowCount++;
}

function removeRow(button) {
    const tbody = document.getElementById('phonesTableBody');
    if (tbody.rows.length > 1) {
        button.closest('tr').remove();
        rowCount--;
    } else {
        showAlert('warning', '至少需要保留一行');
    }
}

function clearAll() {
    const tbody = document.getElementById('phonesTableBody');
    tbody.innerHTML = `
        <tr>
            <td><input type="text" class="form-control form-control-sm" name="imei" required></td>
            <td><input type="text" class="form-control form-control-sm" name="brand" required></td>
            <td><input type="text" class="form-control form-control-sm" name="model" required></td>
            <td><input type="text" class="form-control form-control-sm" name="color" required></td>
            <td><input type="text" class="form-control form-control-sm" name="project" required></td>
            <td>
                <select class="form-select form-select-sm" name="original_owner_id" required>
                    <option value="">选择拥有者</option>
                    {% for user in users %}
                        <option value="{{ user.id }}">{{ user.name }} ({{ user.department }})</option>
                    {% endfor %}
                </select>
            </td>
            <td><input type="text" class="form-control form-control-sm" name="notes"></td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `;
    rowCount = 1;
}

function fillSampleData() {
    const sampleData = [
        { imei: '123456789012351', brand: 'Apple', model: 'iPhone 15', color: '深空黑', project: '研发项目', notes: '新款手机' },
        { imei: '123456789012352', brand: 'Samsung', model: 'Galaxy S24', color: '幻影紫', project: '测试项目', notes: '旗舰机型' },
        { imei: '123456789012353', brand: 'Huawei', model: 'Mate 60', color: '雅川青', project: '市场项目', notes: '商务用机' }
    ];
    
    const tbody = document.getElementById('phonesTableBody');
    tbody.innerHTML = '';
    
    sampleData.forEach((data, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="text" class="form-control form-control-sm" name="imei" value="${data.imei}" required></td>
            <td><input type="text" class="form-control form-control-sm" name="brand" value="${data.brand}" required></td>
            <td><input type="text" class="form-control form-control-sm" name="model" value="${data.model}" required></td>
            <td><input type="text" class="form-control form-control-sm" name="color" value="${data.color}" required></td>
            <td><input type="text" class="form-control form-control-sm" name="project" value="${data.project || '测试项目'}" required></td>
            <td>
                <select class="form-select form-select-sm" name="original_owner_id" required>
                    <option value="">选择拥有者</option>
                    {% for user in users %}
                        <option value="{{ user.id }}">{{ user.name }} ({{ user.department }})</option>
                    {% endfor %}
                </select>
            </td>
            <td><input type="text" class="form-control form-control-sm" name="notes" value="${data.notes}"></td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    rowCount = sampleData.length;
    showAlert('success', '示例数据已填充，请选择初始拥有者');
}

function goToPhoneList() {
    window.location.href = '/phones';
}

document.getElementById('batchAddForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const tbody = document.getElementById('phonesTableBody');
    const rows = tbody.querySelectorAll('tr');
    const phones = [];
    
    // 收集所有行的数据
    for (let row of rows) {
        const inputs = row.querySelectorAll('input, select');
        const phoneData = {};
        let isValid = true;
        
        inputs.forEach(input => {
            if (input.name && input.name !== '') {
                if (input.required && !input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                } else {
                    input.classList.remove('is-invalid');
                    phoneData[input.name] = input.value.trim();
                }
            }
        });
        
        if (isValid && phoneData.imei) {
            // 转换original_owner_id为整数
            phoneData.original_owner_id = parseInt(phoneData.original_owner_id);
            phones.push(phoneData);
        }
    }
    
    if (phones.length === 0) {
        showAlert('danger', '请至少填写一行完整的手机信息');
        return;
    }
    
    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 批量添加中...';
    submitBtn.disabled = true;
    
    fetch('/phones/batch_add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phones: phones })
    })
    .then(response => response.json())
    .then(data => {
        // 显示结果
        let resultHtml = `<h5>${data.message}</h5>`;
        if (data.details && data.details.length > 0) {
            resultHtml += '<ul class="list-unstyled mt-3">';
            data.details.forEach(detail => {
                const isSuccess = detail.startsWith('✓');
                const className = isSuccess ? 'text-success' : 'text-danger';
                resultHtml += `<li class="${className}">${detail}</li>`;
            });
            resultHtml += '</ul>';
        }
        
        document.getElementById('resultContent').innerHTML = resultHtml;
        const modal = new bootstrap.Modal(document.getElementById('resultModal'));
        modal.show();
        
        // 如果全部成功，清空表单
        if (data.success && !data.message.includes('部分成功')) {
            clearAll();
        }
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '批量添加失败，请重试');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// 添加快捷键支持
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        document.getElementById('batchAddForm').dispatchEvent(new Event('submit'));
    } else if (e.ctrlKey && e.key === '=') {
        e.preventDefault();
        addPhoneRow();
    }
});
</script>
{% endblock %}
