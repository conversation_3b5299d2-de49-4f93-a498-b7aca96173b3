{% extends "base.html" %}

{% block title %}编辑手机 - {{ phone.brand }} {{ phone.model }} - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-pencil"></i> 编辑手机
            </h1>
            <div>
                <a href="{{ url_for('phone_detail', imei=phone.imei) }}" class="btn btn-info me-2">
                    <i class="bi bi-eye"></i> 查看详情
                </a>
                <a href="{{ url_for('phones') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回手机列表
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">编辑手机信息</h5>
            </div>
            <div class="card-body">
                <form id="editPhoneForm">
                    <div class="mb-3">
                        <label for="imei" class="form-label">IMEI编码</label>
                        <input type="text" class="form-control" id="imei" name="imei" 
                               value="{{ phone.imei }}" readonly>
                        <div class="form-text">IMEI编码不可修改</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="brand" class="form-label">
                                    品牌 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="brand" name="brand" 
                                       value="{{ phone.brand }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">
                                    型号 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="model" name="model" 
                                       value="{{ phone.model }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">
                                    颜色 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="color" name="color" 
                                       value="{{ phone.color }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="project" class="form-label">
                                    所属项目 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="project" name="project" 
                                       value="{{ phone.project }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="available" {% if phone.status == 'available' %}selected{% endif %}>可用</option>
                            <option value="in_use" {% if phone.status == 'in_use' %}selected{% endif %}>使用中</option>
                        </select>
                        <div class="form-text">注意：修改状态可能会影响借用记录</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ phone.notes }}</textarea>
                        <div class="form-text">可选项，记录手机的其他信息</div>
                    </div>
                    
                    <!-- 不可编辑的信息显示 -->
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">不可修改的信息</h6>
                            <p class="mb-1"><strong>原始拥有者:</strong> {{ phone.original_owner.name if phone.original_owner else '未知' }}</p>
                            <p class="mb-1"><strong>当前持有者:</strong> {{ phone.current_owner.name if phone.current_owner else '未分配' }}</p>
                            <p class="mb-0"><strong>创建时间:</strong> {{ phone.created_at.strftime('%Y-%m-%d %H:%M:%S') if phone.created_at else '未设置' }}</p>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('phone_detail', imei=phone.imei) }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存更改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('editPhoneForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
    submitBtn.disabled = true;
    
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.href = '{{ url_for("phone_detail", imei=phone.imei) }}';
            }, 1500);
        } else {
            showAlert('danger', data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '保存失败，请重试');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// 状态变更提示
document.getElementById('status').addEventListener('change', function() {
    const status = this.value;
    const currentStatus = '{{ phone.status }}';
    
    if (status !== currentStatus) {
        let message = '';
        if (status === 'retired' && currentStatus !== 'retired') {
            message = '将手机设为已退役后，将无法进行借用操作。';
        } else if (status === 'maintenance') {
            message = '维护中的手机暂时无法被借用。';
        } else if (status === 'available' && currentStatus === 'in_use') {
            message = '如果手机当前有活跃的借用记录，请先处理归还。';
        }
        
        if (message) {
            showAlert('warning', message, 8000);
        }
    }
});
</script>
{% endblock %}
