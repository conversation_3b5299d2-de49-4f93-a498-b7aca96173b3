"""
手机管理系统 - Flask Web应用
提供现代化的Web前端界面
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from datetime import datetime
from functools import wraps

from phone_manager import PhoneManager
from models import User

app = Flask(__name__)
app.secret_key = 'phone_manager_secret_key_2024'

# 初始化手机管理器
manager = PhoneManager()

# 初始化Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'  # 未登录用户访问受保护页面时，将重定向到登录页面
login_manager.login_message = "请先登录以访问此页面。"
login_manager.login_message_category = "info"

@login_manager.user_loader
def load_user(user_id):
    """Flask-Login要求的回调函数，用于通过用户ID加载用户对象"""
    return manager.get_user(int(user_id))

# --- 装饰器 --- #
def admin_required(f):
    """检查管理员权限的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('此操作需要管理员权限。', 'error')
            return redirect(url_for('index'))
        return f(*args, **kwargs)
    return decorated_function

# --- 模板上下文处理器 --- #
@app.context_processor
def inject_datetime():
    """向模板注入datetime对象"""
    return {'datetime': datetime}

# --- 自定义模板过滤器 --- #
@app.template_filter('date')
def date_filter(date_obj):
    """日期格式化过滤器"""
    if date_obj is None:
        return ''
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.fromisoformat(date_obj)
        except ValueError:
            return date_obj
    return date_obj.strftime('%Y-%m-%d')

# --- 认证路由 --- #

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        name = request.form.get('name')
        department = request.form.get('department')
        email = request.form.get('email')
        phone_number = request.form.get('phone_number')
        password = request.form.get('password')

        if not all([name, department, email, password]):
            flash('所有字段均为必填项。', 'error')
            return redirect(url_for('register'))

        existing_user = manager.db.get_user_by_email(email)
        if existing_user:
            flash('该邮箱已被注册。', 'error')
            return redirect(url_for('register'))

        # 判断是否是第一个注册的用户
        is_first_user = not manager.list_users()
        role = 'admin' if is_first_user else 'user'

        success, message = manager.create_user(
            name, department, email, phone_number, password, role
        )
        
        if success:
            flash(f'注册成功！第一个用户 ({name}) 已被设为管理员。' if is_first_user else '注册成功，请登录。', 'success')
            return redirect(url_for('login'))
        else:
            flash(f'注册失败: {message}', 'error')
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        user = manager.db.get_user_by_email(email)
        
        if user and user.check_password(password):
            login_user(user)
            flash('登录成功！', 'success')
            return redirect(url_for('index'))
        else:
            flash('邮箱或密码错误。', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """用户注销"""
    logout_user()
    flash('您已成功注销。', 'info')
    return redirect(url_for('login'))

# --- 核心功能路由 --- #

@app.route('/')
@login_required
def index():
    """首页 - 显示系统概览"""
    stats = manager.get_statistics()
    recent_phones = manager.list_phones()[:5]
    overdue_loans = manager.get_overdue_loans()
    
    return render_template('index.html', 
                         stats=stats, 
                         recent_phones=recent_phones,
                         overdue_loans=overdue_loans)

# 用户管理路由
@app.route('/users')
@login_required
@admin_required
def users():
    """用户管理页面"""
    keyword = request.args.get('keyword', '').strip()
    sort_by = request.args.get('sort_by', 'id')
    
    if keyword or sort_by != 'id':
        users_list = manager.search_users(keyword, sort_by)
    else:
        # 默认按ID排序
        users_list = manager.search_users("", "id")
    
    return render_template('users.html', 
                         users=users_list,
                         search_params={
                             'keyword': keyword,
                             'sort_by': sort_by
                         })

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_user():
    """添加用户"""
    if request.method == 'POST':
        data = request.get_json()
        success, message = manager.create_user(
            data['name'], 
            data['department'], 
            data['email'], 
            data.get('phone_number', ''),
            data.get('password', '123456'), # 批量添加时给一个默认密码
            data.get('role', 'user')
        )
        return jsonify({'success': success, 'message': message})
    
    return render_template('add_user.html')

@app.route('/users/<int:user_id>')
@login_required
def user_detail(user_id):
    """用户详情页面"""
    # 普通用户只能看自己的详情，管理员可以看所有人的
    if current_user.role != 'admin' and current_user.id != user_id:
        flash('您没有权限查看此页面。', 'error')
        return redirect(url_for('index'))

    user = manager.get_user(user_id)
    if not user:
        flash('用户不存在', 'error')
        # 如果是管理员，返回用户列表；否则返回首页
        if current_user.role == 'admin':
            return redirect(url_for('users'))
        else:
            return redirect(url_for('index'))
    
    phones = manager.get_phones_by_user(user_id, current_only=False)
    return render_template('user_detail.html', user=user, phones=phones)

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(user_id):
    """编辑用户"""
    user = manager.get_user(user_id)
    if not user:
        flash('用户不存在', 'error')
        return redirect(url_for('users'))
    
    if request.method == 'POST':
        data = request.get_json()
        success, message = manager.update_user(
            user_id,
            data.get('name'),
            data.get('department'),
            data.get('email'),
            data.get('phone_number'),
            data.get('role')
        )
        return jsonify({'success': success, 'message': message})
    
    return render_template('edit_user.html', user=user)

@app.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_user(user_id):
    """删除用户"""
    # 防止管理员删除自己
    if current_user.id == user_id:
        return jsonify({'success': False, 'message': '不能删除自己。'})

    success, message = manager.delete_user(user_id)
    return jsonify({'success': success, 'message': message})

# 手机管理路由
@app.route('/phones')
@login_required
def phones():
    """手机管理页面"""
    # ... (rest of the function is the same)
    keyword = request.args.get('keyword', '')
    project = request.args.get('project', '')
    status = request.args.get('status', '')
    user_id = request.args.get('user_id', type=int)
    
    if keyword or project or status or user_id:
        phones_list = manager.search_phones(keyword, status, user_id, project)
    else:
        phones_list = manager.list_phones()
    
    users_list = manager.list_users()
    active_loans = manager.get_active_loans()
    
    return render_template('phones.html', 
                         phones=phones_list, 
                         users=users_list,
                         active_loans=active_loans,
                         search_params={
                             'keyword': keyword,
                             'project': project,
                             'status': status,
                             'user_id': user_id
                         })

@app.route('/phones/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_phone():
    """添加手机"""
    if request.method == 'POST':
        data = request.get_json()
        success, message = manager.add_phone(
            data['imei'], data['brand'], data['model'], data['color'],
            data['project'], data['original_owner_id'], data.get('notes', '')
        )
        return jsonify({'success': success, 'message': message})
    
    users_list = manager.list_users()
    return render_template('add_phone.html', users=users_list)

@app.route('/phones/<imei>')
@login_required
def phone_detail(imei):
    """手机详情页面"""
    # ... (rest of the function is the same)
    phone = manager.get_phone(imei)
    if not phone:
        flash('手机不存在', 'error')
        return redirect(url_for('phones'))
    
    original_owner = manager.get_user(phone.original_owner_id)
    current_owner = manager.get_user(phone.current_owner_id) if phone.current_owner_id else None
    active_loans = manager.get_active_loans(imei)
    history = manager.get_phone_history(imei)
    
    # 为active_loans填充用户信息
    for loan in active_loans:
        loan.borrower = manager.get_user(loan.borrower_id)
        loan.lender = manager.get_user(loan.lender_id)
    
    return render_template('phone_detail.html', 
                         phone=phone,
                         original_owner=original_owner,
                         current_owner=current_owner,
                         active_loans=active_loans,
                         history=history)

@app.route('/phones/<imei>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_phone(imei):
    """编辑手机"""
    # ... (rest of the function is the same)
    phone = manager.get_phone(imei)
    if not phone:
        flash('手机不存在', 'error')
        return redirect(url_for('phones'))
    
    if request.method == 'POST':
        data = request.get_json()
        success, message = manager.update_phone(
            imei, data.get('brand'), data.get('model'), data.get('color'),
            data.get('project'), data.get('status'), data.get('notes')
        )
        return jsonify({'success': success, 'message': message})
    
    return render_template('edit_phone.html', phone=phone)

@app.route('/phones/<imei>/delete', methods=['POST'])
@login_required
@admin_required
def delete_phone(imei):
    """删除手机"""
    success, message = manager.delete_phone(imei)
    return jsonify({'success': success, 'message': message})

@app.route('/phones/<imei>/trace')
@login_required
def phone_trace(imei):
    """手机追溯页面"""
    phone = manager.get_phone(imei)
    if not phone:
        flash('手机不存在', 'error')
        return redirect(url_for('phones'))
    
    success, trace_info = manager.get_phone_trace(imei)
    if not success:
        flash(f'获取追溯信息失败: {trace_info}', 'error')
        return redirect(url_for('phone_detail', imei=imei))
    
    # 获取详细信息用于模板渲染
    original_owner = manager.get_user(phone.original_owner_id)
    current_owner = manager.get_user(phone.current_owner_id) if phone.current_owner_id else None
    history = manager.get_phone_history(imei)
    active_loans = manager.get_active_loans(imei)
    
    # 为active_loans填充用户信息
    for loan in active_loans:
        loan.borrower = manager.get_user(loan.borrower_id)
        loan.lender = manager.get_user(loan.lender_id)
    
    return render_template('phone_trace.html',
                         phone=phone,
                         original_owner=original_owner,
                         current_owner=current_owner,
                         history=history,
                         active_loans=active_loans,
                         trace_info=trace_info)

@app.route('/loans')
@login_required
def loans():
    """借用管理页面"""
    try:
        active_loans = manager.get_active_loans()
        overdue_loans = manager.get_overdue_loans()
        
        # 为每个借用记录添加相关信息，并进行空值检查
        for loan in active_loans:
            loan.phone = manager.get_phone(loan.phone_imei)
            loan.borrower = manager.get_user(loan.borrower_id)
            loan.lender = manager.get_user(loan.lender_id)
            
            # 如果相关数据不存在，创建默认对象避免页面崩溃
            if not loan.phone:
                # 创建一个默认的手机对象
                class DefaultPhone:
                    def __init__(self):
                        self.brand = '未知'
                        self.model = '手机不存在'
                        self.color = ''
                        self.status = '未知'
                loan.phone = DefaultPhone()
            
            if not loan.borrower:
                class DefaultUser:
                    def __init__(self):
                        self.name = '未知用户'
                        self.department = ''
                loan.borrower = DefaultUser()
            
            if not loan.lender:
                class DefaultUser:
                    def __init__(self):
                        self.name = '未知用户'
                        self.department = ''
                loan.lender = DefaultUser()
        
        for loan in overdue_loans:
            loan.phone = manager.get_phone(loan.phone_imei)
            loan.borrower = manager.get_user(loan.borrower_id)
            loan.lender = manager.get_user(loan.lender_id)
            
            # 同样处理逾期借用记录
            if not loan.phone:
                class DefaultPhone:
                    def __init__(self):
                        self.brand = '未知'
                        self.model = '手机不存在'
                        self.color = ''
                        self.status = '未知'
                loan.phone = DefaultPhone()
            
            if not loan.borrower:
                class DefaultUser:
                    def __init__(self):
                        self.name = '未知用户'
                        self.department = ''
                loan.borrower = DefaultUser()
            
            if not loan.lender:
                class DefaultUser:
                    def __init__(self):
                        self.name = '未知用户'
                        self.department = ''
                loan.lender = DefaultUser()
        
        return render_template('loans.html',
                             active_loans=active_loans,
                             overdue_loans=overdue_loans)
                             
    except Exception as e:
        flash(f'加载借用管理页面失败: {str(e)}', 'error')
        return render_template('loans.html',
                             active_loans=[],
                             overdue_loans=[])

@app.route('/loans/new', methods=['GET', 'POST'])
@login_required
def new_loan():
    """创建新借用记录"""
    if request.method == 'POST':
        data = request.get_json()
        
        # 获取手机信息确定出借者
        phone = manager.get_phone(data['phone_imei'])
        if not phone:
            return jsonify({'success': False, 'message': '手机不存在'})
        
        if phone.status != 'available':
            return jsonify({'success': False, 'message': f'手机状态为 {phone.status}，无法借出'})
        
        # 出借者是手机的当前持有者
        lender_id = phone.current_owner_id
        if not lender_id:
            return jsonify({'success': False, 'message': '手机没有当前持有者，无法借出'})
        
        # 解析预期归还日期
        expected_return_date = None
        if data.get('expected_return_date'):
            try:
                expected_return_date = datetime.fromisoformat(data['expected_return_date'])
            except ValueError:
                try:
                    expected_return_date = datetime.strptime(data['expected_return_date'], '%Y-%m-%d')
                except ValueError:
                    pass  # 日期格式错误，但不影响借用创建
        
        success, message = manager.loan_phone(
            data['phone_imei'],
            data['borrower_id'],
            lender_id,
            expected_return_date,
            data.get('purpose', ''),
            data.get('notes', '')
        )
        return jsonify({'success': success, 'message': message})
    
    # GET 请求
    phone_imei = request.args.get('phone_imei')
    selected_phone = None
    if phone_imei:
        selected_phone = manager.get_phone(phone_imei)
    
    # 获取所有用户列表
    users_list = manager.list_users()
    
    return render_template('new_loan.html',
                         users=users_list,
                         selected_phone=selected_phone)

@app.route('/loans/<int:record_id>/return', methods=['POST'])
@login_required
def return_loan(record_id):
    """归还手机"""
    success, message = manager.return_phone(record_id, current_user.id)
    return jsonify({'success': success, 'message': message})

@app.route('/phones/batch_add', methods=['GET', 'POST'])
@login_required
@admin_required
def batch_add_phones():
    """批量添加手机"""
    if request.method == 'POST':
        try:
            # 检查请求是否为JSON格式（来自前端Ajax请求）
            if request.is_json:
                data = request.get_json()
                phones_data = data.get('phones', [])
                
                if not phones_data:
                    return jsonify({'success': False, 'message': '请至少添加一台手机。'})
                
                success, message, details = manager.batch_add_phones(phones_data)
                return jsonify({
                    'success': success, 
                    'message': message,
                    'details': details
                })
            
            # 处理传统表单提交（备用方案）
            else:
                phones_data = []
                
                # 从表单中获取批量数据
                imeis = request.form.getlist('imei')
                brands = request.form.getlist('brand')
                models = request.form.getlist('model')
                colors = request.form.getlist('color')
                projects = request.form.getlist('project')
                owner_ids = request.form.getlist('owner_id')
                notes_list = request.form.getlist('notes')
                
                # 组装数据
                for i in range(len(imeis)):
                    if imeis[i].strip():  # 只处理非空的IMEI
                        phones_data.append({
                            'imei': imeis[i].strip(),
                            'brand': brands[i].strip() if i < len(brands) else '',
                            'model': models[i].strip() if i < len(models) else '',
                            'color': colors[i].strip() if i < len(colors) else '',
                            'project': projects[i].strip() if i < len(projects) else '',
                            'original_owner_id': int(owner_ids[i]) if i < len(owner_ids) and owner_ids[i] else None,
                            'notes': notes_list[i].strip() if i < len(notes_list) else ''
                        })
                
                if phones_data:
                    success, message, details = manager.batch_add_phones(phones_data)
                    if success:
                        flash(f'批量添加完成！{message}', 'success')
                        # 检查是否有失败的项目
                        if any('✗' in detail for detail in details):
                            flash('部分手机添加失败，请查看详情', 'warning')
                        return redirect(url_for('phones'))
                    else:
                        flash(f'批量添加失败：{message}', 'error')
                else:
                    flash('请至少添加一台手机。', 'error')
        
        except Exception as e:
            if request.is_json:
                return jsonify({'success': False, 'message': f'处理请求时出错: {str(e)}'})
            else:
                flash(f'批量添加失败：{str(e)}', 'error')
    
    users = manager.list_users()
    return render_template('batch_add_phones.html', users=users)

# --- API 路由 --- #
@app.route('/api/stats')
@login_required
def api_stats():
    """获取系统统计信息API"""
    try:
        stats = manager.get_statistics()
        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>/phones')
@login_required
def api_user_phones(user_id):
    """获取用户的可用手机列表"""
    try:
        # 获取用户拥有的所有手机
        phones = manager.get_phones_by_user(user_id, current_only=True)
        # 过滤出可用的手机
        available_phones = []
        for phone in phones:
            if phone.status == 'available':
                available_phones.append({
                    'imei': phone.imei,
                    'brand': phone.brand,
                    'model': phone.model,
                    'color': phone.color,
                    'status': phone.status
                })
        
        return jsonify(available_phones)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/phones/search')
@login_required
def api_search_phones():
    """手机搜索API"""
    try:
        keyword = request.args.get('keyword', '')
        project = request.args.get('project', '')
        status = request.args.get('status', '')
        user_id = request.args.get('user_id', type=int)
        
        if keyword or project or status or user_id:
            phones_list = manager.search_phones(keyword, status, user_id, project)
        else:
            phones_list = manager.list_phones()
        
        users_list = manager.list_users()
        active_loans = manager.get_active_loans()
        
        # 为每个借用记录添加相关信息
        for loan in active_loans:
            loan.phone = manager.get_phone(loan.phone_imei)
            loan.borrower = manager.get_user(loan.borrower_id)
            loan.lender = manager.get_user(loan.lender_id)
        
        # 渲染手机列表部分的模板
        return render_template('phones_ajax.html', 
                             phones=phones_list, 
                             users=users_list,
                             active_loans=active_loans,
                             search_params={
                                 'keyword': keyword,
                                 'project': project,
                                 'status': status,
                                 'user_id': user_id
                             })
    except Exception as e:
        return f'<div class="alert alert-danger">搜索失败: {str(e)}</div>', 500

@app.route('/api/phones/<imei>/loan-chain')
@login_required
def api_phone_loan_chain(imei):
    """获取手机的借用链信息"""
    try:
        success, message, chain_info = manager.get_loan_chain_info(imei)
        return jsonify({
            'success': success,
            'message': message,
            'chain': chain_info
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)