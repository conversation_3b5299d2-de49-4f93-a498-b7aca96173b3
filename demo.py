#!/usr/bin/env python3
"""
手机管理系统 - 演示脚本
创建一些示例数据来演示系统功能
"""

import os
from datetime import datetime, timedelta
from phone_manager import PhoneManager

def create_demo_data():
    """创建演示数据"""
    print("=" * 60)
    print("  手机管理系统 - 创建演示数据")
    print("=" * 60)
    
    # 删除现有数据库文件（如果存在）
    if os.path.exists("phone_manager.db"):
        os.remove("phone_manager.db")
        print("已清除现有数据")
    
    # 初始化管理器
    manager = PhoneManager()
    
    try:
        # 创建用户
        print("\n1. 创建用户...")
        users_data = [
            ("张三", "技术部", "<EMAIL>", "13800138000"),
            ("李四", "产品部", "<EMAIL>", "13800138001"),
            ("王五", "设计部", "<EMAIL>", "13800138002"),
            ("赵六", "测试部", "z<PERSON><PERSON><PERSON>@company.com", "13800138003"),
            ("钱七", "运营部", "<EMAIL>", "13800138004"),
        ]
        
        user_ids = []
        for name, dept, email, phone in users_data:
            success, msg = manager.create_user(name, dept, email, phone)
            if success:
                # 获取用户ID
                users = manager.list_users()
                user_id = next(u.id for u in users if u.name == name)
                user_ids.append(user_id)
                print(f"   ✓ 创建用户: {name} (ID: {user_id})")
            else:
                print(f"   ✗ 创建用户失败: {name} - {msg}")
        
        # 创建手机
        print("\n2. 创建手机...")
        phones_data = [
            ("123456789012345", "Apple", "iPhone 14 Pro", "深空黑", "研发项目", user_ids[0], "公司配发手机"),
            ("123456789012346", "Samsung", "Galaxy S23", "幻影黑", "测试项目", user_ids[1], "测试专用机"),
            ("123456789012347", "Huawei", "Mate 50", "昆仑破晓", "设计项目", user_ids[2], "设计部工作手机"),
            ("123456789012348", "Xiaomi", "13 Pro", "陶瓷黑", "研发项目", user_ids[0], "备用机"),
            ("123456789012349", "OPPO", "Find X5", "雪山白", "测试项目", user_ids[3], "测试机型"),
            ("123456789012350", "vivo", "X90", "华夏红", "市场项目", user_ids[4], "运营推广用机"),
        ]
        
        for imei, brand, model, color, project, owner_id, notes in phones_data:
            success, msg = manager.add_phone(imei, brand, model, color, project, owner_id, notes)
            if success:
                print(f"   ✓ 添加手机: {brand} {model} (IMEI: {imei})")
            else:
                print(f"   ✗ 添加手机失败: {brand} {model} - {msg}")
        
        # 创建借用记录
        print("\n3. 创建借用记录...")
        loans_data = [
            ("123456789012345", user_ids[0], user_ids[2], datetime.now() + timedelta(days=30), "项目开发使用"),
            ("123456789012346", user_ids[1], user_ids[4], datetime.now() + timedelta(days=15), "市场调研"),
            ("123456789012349", user_ids[3], user_ids[1], datetime.now() + timedelta(days=-5), "功能测试"),  # 逾期记录
        ]
        
        for phone_imei, lender_id, borrower_id, expected_return, purpose in loans_data:
            success, msg = manager.loan_phone(phone_imei, borrower_id, lender_id, expected_return, purpose)
            if success:
                borrower = manager.get_user(borrower_id)
                status = "逾期" if expected_return < datetime.now() else "正常"
                print(f"   ✓ 创建借用: {phone_imei} -> {borrower.name} ({status})")
            else:
                print(f"   ✗ 创建借用失败: {phone_imei} - {msg}")
        
        # 显示统计信息
        print("\n4. 系统统计...")
        stats = manager.get_statistics()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        print("\n" + "=" * 60)
        print("  演示数据创建完成！")
        print("  现在可以启动Web界面查看系统功能")
        print("  运行命令: python run_web.py")
        print("  访问地址: http://localhost:5000")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n创建演示数据时出错: {e}")
        return False

if __name__ == '__main__':
    create_demo_data()
