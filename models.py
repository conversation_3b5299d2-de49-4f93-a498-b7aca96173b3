"""
手机管理系统 - 数据模型
包含手机、用户和借用记录的数据模型
"""

from datetime import datetime
from typing import Optional, List
from dataclasses import dataclass, field
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash


@dataclass
class User(UserMixin):
    """用户数据模型"""
    id: int
    name: str
    department: str
    email: str
    phone_number: str
    password_hash: str = None
    role: str = 'user'  # 'user' or 'admin'
    created_at: datetime = field(default_factory=datetime.now)

    def set_password(self, password):
        """设置密码，自动进行哈希存储"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """校验密码"""
        return check_password_hash(self.password_hash, password)
    
    def __str__(self):
        return f"{self.name} ({self.department})"


@dataclass
class Phone:
    """手机数据模型"""
    imei: str  # 手机唯一标识符
    brand: str
    model: str
    color: str
    project: str  # 所属项目
    original_owner_id: int  # 初始拥有者ID
    current_owner_id: Optional[int] = None  # 当前使用者ID，None表示未分配
    status: str = "available"  # available, in_use, maintenance, retired
    notes: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    
    def __str__(self):
        return f"{self.brand} {self.model} (IMEI: {self.imei})"


@dataclass
class PhoneLoanRecord:
    """手机借用记录数据模型"""
    id: int
    phone_imei: str
    borrower_id: int  # 借用者ID
    lender_id: int    # 出借者ID（当前持有者）
    loan_date: datetime
    expected_return_date: Optional[datetime] = None
    actual_return_date: Optional[datetime] = None
    purpose: str = ""  # 借用目的
    status: str = "active"  # active, returned, overdue
    notes: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    
    def __str__(self):
        status_str = "已归还" if self.status == "returned" else "借用中"
        return f"借用记录 {self.id}: {self.phone_imei} - {status_str}"


@dataclass
class PhoneHistory:
    """手机历史记录数据模型"""
    id: int
    phone_imei: str
    action: str  # created, assigned, returned, transferred, maintenance, retired
    from_user_id: Optional[int] = None
    to_user_id: Optional[int] = None
    timestamp: datetime = field(default_factory=datetime.now)
    description: str = ""
    
    def __str__(self):
        return f"{self.timestamp.strftime('%Y-%m-%d %H:%M')} - {self.action}: {self.description}"