{% extends "base.html" %}

{% block title %}{{ phone.brand }} {{ phone.model }} - 手机详情 - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-phone"></i> 手机详情
            </h1>
            <div>
                <a href="{{ url_for('phone_trace', imei=phone.imei) }}" class="btn btn-success me-2">
                    <i class="bi bi-diagram-3"></i> 追溯
                </a>
                <a href="{{ url_for('edit_phone', imei=phone.imei) }}" class="btn btn-warning me-2">
                    <i class="bi bi-pencil"></i> 编辑
                </a>
                <a href="{{ url_for('phones') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回手机列表
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 手机基本信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-phone-vibrate"></i> 基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">IMEI编码:</th>
                                <td>
                                    <code>{{ phone.imei }}</code>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" 
                                            onclick="copyToClipboard('{{ phone.imei }}')" 
                                            title="复制IMEI">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <th>品牌型号:</th>
                                <td><strong>{{ phone.brand }} {{ phone.model }}</strong></td>
                            </tr>
                                                            <tr>
                                    <th>颜色:</th>
                                    <td>{{ phone.color }}</td>
                                </tr>
                                <tr>
                                    <th>所属项目:</th>
                                    <td>{{ phone.project }}</td>
                                </tr>

                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">当前状态:</th>
                                <td>
                                    {% if phone.status == 'available' %}
                                        <span class="badge bg-success">可用</span>
                                    {% elif phone.status == 'in_use' and phone.current_owner_id != phone.original_owner_id %}
                                        <span class="badge bg-primary">使用中</span>
                                    {% elif phone.status == 'in_use' and phone.current_owner_id == phone.original_owner_id %}
                                        <span class="badge bg-success">可用</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">{{ phone.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>原始拥有者:</th>
                                <td>
                                    {% if original_owner %}
                                        <a href="{{ url_for('user_detail', user_id=original_owner.id) }}">
                                            <strong>{{ original_owner.name }}</strong>
                                        </a>
                                        <small class="text-muted">({{ original_owner.department }})</small>
                                    {% else %}
                                        <span class="text-muted">未知用户</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>当前持有者:</th>
                                <td>
                                    {% if current_owner %}
                                        <a href="{{ url_for('user_detail', user_id=current_owner.id) }}">
                                            <strong>{{ current_owner.name }}</strong>
                                        </a>
                                        <small class="text-muted">({{ current_owner.department }})</small>
                                    {% else %}
                                        <span class="text-muted">未分配</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>入库时间:</th>
                                <td>{{ phone.created_at.strftime('%Y-%m-%d %H:%M:%S') if phone.created_at else '未设置' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                {% if phone.notes %}
                    <div class="mt-3">
                        <h6>备注:</h6>
                        <div class="alert alert-light">
                            {{ phone.notes }}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> 快捷操作
                </h5>
            </div>
            <div class="card-body">
                {% if phone.status == 'available' %}
                    <a href="{{ url_for('new_loan') }}?phone_imei={{ phone.imei }}" 
                       class="btn btn-primary w-100 mb-2">
                        <i class="bi bi-arrow-right-circle"></i> 借出手机
                    </a>
                {% endif %}
                
                <a href="{{ url_for('phone_trace', imei=phone.imei) }}" 
                   class="btn btn-success w-100 mb-2">
                    <i class="bi bi-diagram-3"></i> 查看追溯
                </a>
                
                <a href="{{ url_for('edit_phone', imei=phone.imei) }}" 
                   class="btn btn-warning w-100 mb-2">
                    <i class="bi bi-pencil"></i> 编辑信息
                </a>
                
                <button class="btn btn-outline-danger w-100" 
                        onclick="deletePhone('{{ phone.imei }}', '{{ phone.brand }} {{ phone.model }}')">
                    <i class="bi bi-trash"></i> 删除手机
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 当前借用状态 -->
{% if active_loans %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <div class="d-flex justify-content-between align-items-start">
                <h5 class="alert-heading">
                    <i class="bi bi-info-circle"></i> 当前借用状态
                </h5>
                <button type="button" class="btn btn-sm btn-outline-info" onclick="showLoanChain('{{ phone.imei }}')">
                    <i class="bi bi-diagram-3"></i> 查看借用链
                </button>
            </div>
            {% for loan in active_loans %}
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>借用者:</strong> 
                            {% if loan.borrower %}
                                <a href="{{ url_for('user_detail', user_id=loan.borrower_id) }}">
                                    {{ loan.borrower.name }}
                                </a> ({{ loan.borrower.department }})
                            {% else %}
                                未知用户
                            {% endif %}
                        </p>
                        <p class="mb-1"><strong>出借者:</strong> 
                            {% if loan.lender %}
                                <a href="{{ url_for('user_detail', user_id=loan.lender_id) }}">
                                    {{ loan.lender.name }}
                                </a> ({{ loan.lender.department }})
                            {% else %}
                                未知用户
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>借用日期:</strong> {{ loan.loan_date|date }}</p>
                        {% if loan.expected_return_date %}
                            <p class="mb-1"><strong>预期归还:</strong> {{ loan.expected_return_date|date }}</p>
                        {% endif %}
                    </div>
                </div>
                {% if loan.purpose %}
                    <p class="mb-1"><strong>借用目的:</strong> {{ loan.purpose }}</p>
                {% endif %}
                <div class="mt-3">
                    <button class="btn btn-success btn-sm" 
                            onclick="returnPhone({{ loan.id }}, '{{ phone.imei }}')">
                        <i class="bi bi-check-circle"></i> 归还手机
                    </button>
                </div>
                {% if not loop.last %}<hr>{% endif %}
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- 历史记录 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> 最近历史记录
                </h5>
            </div>
            <div class="card-body">
                {% if history %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>操作</th>
                                    <th>描述</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in history[:10] %}
                                    <tr>
                                        <td>{{ record.timestamp.strftime('%Y-%m-%d %H:%M:%S') if record.timestamp else '未设置' }}</td>
                                        <td>
                                            {% if record.action == 'created' %}
                                                <span class="badge bg-success">创建</span>
                                            {% elif record.action == 'transferred' %}
                                                <span class="badge bg-primary">转移</span>
                                            {% elif record.action == 'returned' %}
                                                <span class="badge bg-info">归还</span>
                                            {% elif record.action == 'maintenance' %}
                                                <span class="badge bg-warning">维护</span>
                                            {% elif record.action == 'retired' %}
                                                <span class="badge bg-secondary">退役</span>
                                            {% else %}
                                                <span class="badge bg-light text-dark">{{ record.action }}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ record.description }}</td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if history|length > 10 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('phone_trace', imei=phone.imei) }}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-right"></i> 查看完整历史记录
                            </a>
                        </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-clock-history display-4 text-muted"></i>
                        <h5 class="mt-3 text-muted">暂无历史记录</h5>
                        <p class="text-muted">该手机还没有任何操作记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除手机 <strong id="deletePhoneName"></strong> 吗？</p>
                <p class="text-danger small">注意：如果手机有活跃的借用记录，将无法删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 归还确认模态框 -->
<div class="modal fade" id="returnModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认归还</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要归还手机 <strong id="returnPhoneImei"></strong> 吗？</p>
                <p class="text-info small">归还后，手机将回到出借者手中。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmReturn">确认归还</button>
            </div>
        </div>
    </div>
</div>

<!-- 借用链模态框 -->
<div class="modal fade" id="loanChainModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-diagram-3"></i> 手机借用链
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="loanChainContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在获取借用链信息...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let deletePhoneImei = null;
let returnLoanId = null;

function deletePhone(imei, phoneName) {
    deletePhoneImei = imei;
    document.getElementById('deletePhoneName').textContent = phoneName;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function returnPhone(loanId, phoneImei) {
    returnLoanId = loanId;
    document.getElementById('returnPhoneImei').textContent = phoneImei;
    const modal = new bootstrap.Modal(document.getElementById('returnModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (deletePhoneImei) {
        fetch(`/phones/${deletePhoneImei}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    window.location.href = '/phones';
                }, 1500);
            } else {
                showAlert('danger', data.message);
            }
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '删除失败，请重试');
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        });
    }
});

document.getElementById('confirmReturn').addEventListener('click', function() {
    if (returnLoanId) {
        const submitBtn = this;
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
        submitBtn.disabled = true;
        
        fetch(`/loans/${returnLoanId}/return`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', data.message);
            }
            bootstrap.Modal.getInstance(document.getElementById('returnModal')).hide();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '归还失败，请重试');
            bootstrap.Modal.getInstance(document.getElementById('returnModal')).hide();
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    }
});

// 复制IMEI到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('success', 'IMEI已复制到剪贴板');
    }, function(err) {
        showAlert('danger', '复制失败');
    });
}

// 显示借用链
function showLoanChain(phoneImei) {
    const modal = new bootstrap.Modal(document.getElementById('loanChainModal'));
    modal.show();
    
    // 获取借用链信息
    fetch(`/api/phones/${phoneImei}/loan-chain`)
        .then(response => response.json())
        .then(data => {
            const content = document.getElementById('loanChainContent');
            
            if (data.success && data.chain.length > 0) {
                let html = `
                    <div class="alert alert-info">
                        <strong><i class="bi bi-info-circle"></i> ${data.message}</strong>
                    </div>
                    <div class="loan-chain">
                `;
                
                data.chain.forEach((loan, index) => {
                    const isCurrentHolder = loan.is_current_holder;
                    const cardClass = isCurrentHolder ? 'border-primary' : 'border-secondary';
                    const badgeClass = isCurrentHolder ? 'bg-primary' : 'bg-secondary';
                    
                    html += `
                        <div class="card mb-3 ${cardClass}">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <span class="badge ${badgeClass}">第 ${loan.step} 步</span>
                                    ${loan.lender_name} → ${loan.borrower_name}
                                </h6>
                                ${isCurrentHolder ? '<span class="badge bg-warning text-dark">当前持有者</span>' : ''}
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>借用日期:</strong> ${new Date(loan.loan_date).toLocaleDateString()}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>记录ID:</strong> ${loan.id}</p>
                                    </div>
                                </div>
                                ${loan.purpose ? `<p class="mb-0"><strong>借用目的:</strong> ${loan.purpose}</p>` : ''}
                            </div>
                        </div>
                    `;
                });
                
                html += `
                    </div>
                    <div class="alert alert-warning">
                        <strong><i class="bi bi-exclamation-triangle"></i> 重要提示</strong><br>
                        归还手机时必须按照借用的逆序进行，即当前持有者先归还，然后依次向上归还。
                    </div>
                `;
                
                content.innerHTML = html;
            } else if (data.success) {
                content.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-check-circle display-4 text-success"></i>
                        <h5 class="mt-3">没有活跃的借用记录</h5>
                        <p class="text-muted">该手机当前没有被借出</p>
                    </div>
                `;
            } else {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>错误:</strong> ${data.error || '获取借用链信息失败'}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('loanChainContent').innerHTML = `
                <div class="alert alert-danger">
                    <strong>错误:</strong> 获取借用链信息失败
                </div>
            `;
        });
}
</script>
{% endblock %}
