{% extends "base.html" %}

{% block title %}添加用户 - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-person-plus"></i> 添加用户
            </h1>
            <a href="{{ url_for('users') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> 返回用户列表
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">用户信息</h5>
            </div>
            <div class="card-body">
                <form id="addUserForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">
                            姓名 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="department" class="form-label">
                            部门 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="department" name="department" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            邮箱 <span class="text-danger">*</span>
                        </label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="form-text">请输入有效的邮箱地址</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone_number" class="form-label">电话号码</label>
                        <input type="tel" class="form-control" id="phone_number" name="phone_number">
                        <div class="form-text">可选项</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('users') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 添加用户
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 添加中...';
    submitBtn.disabled = true;
    
    fetch('/users/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.href = '/users';
            }, 1500);
        } else {
            showAlert('danger', data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '添加失败，请重试');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
