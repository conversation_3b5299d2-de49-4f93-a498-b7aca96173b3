{% extends "base.html" %}

{% block title %}手机管理 - 手机管理系统{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-5">
    <div>
        <h1 class="h2 mb-1 fw-bold">手机管理</h1>
        <p class="text-muted mb-0">设备清单与状态管理</p>
    </div>
    <div class="d-flex gap-2">
        <a href="{{ url_for('batch_add_phones') }}" class="btn btn-outline-primary">
            <i class="bi bi-plus-square me-2"></i>批量添加
        </a>
        <a href="{{ url_for('add_phone') }}" class="btn btn-primary">
            <i class="bi bi-plus me-2"></i>添加手机
        </a>
    </div>
</div>

<!-- 搜索筛选 -->
<div class="search-filters mb-4">
    <div class="card">
        <div class="card-body">
                <form id="searchForm">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="keyword" class="form-label">关键词</label>
                            <input type="text" class="form-control" id="keyword" name="keyword" 
                                   value="{{ search_params.keyword }}" 
                                   placeholder="品牌、型号、IMEI、颜色">
                        </div>
                        <div class="col-md-2">
                            <label for="project" class="form-label">所属项目</label>
                            <input type="text" class="form-control" id="project" name="project"
                                   value="{{ search_params.project }}"
                                   placeholder="项目名称">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部</option>
                                <option value="available" {% if search_params.status == 'available' %}selected{% endif %}>可用</option>
                                <option value="in_use" {% if search_params.status == 'in_use' %}selected{% endif %}>使用中</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="user_id" class="form-label">当前持有者</label>
                            <select class="form-select" id="user_id" name="user_id">
                                <option value="">全部用户</option>
                                {% for user in users %}
                                    <option value="{{ user.id }}" {% if search_params.user_id == user.id %}selected{% endif %}>
                                        {{ user.name }} ({{ user.department }})
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="bi bi-x-circle"></i> 清除
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 手机列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            手机列表 
            {% if phones %}
                <span class="badge bg-light text-dark ms-2">{{ phones|length }}</span>
            {% endif %}
        </h5>
        {% if search_params.keyword or search_params.status or search_params.user_id %}
            <a href="{{ url_for('phones') }}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-x-circle me-1"></i>清除筛选
            </a>
        {% endif %}
    </div>
            <div class="card-body" id="phoneListContainer">
                {% if phones %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th class="fw-semibold">IMEI</th>
                                    <th class="fw-semibold">品牌型号</th>
                                    <th class="fw-semibold">颜色</th>
                                    <th class="fw-semibold">所属项目</th>
                                    <th class="fw-semibold">状态</th>
                                    <th class="fw-semibold">当前持有者</th>
                                    <th class="fw-semibold text-center">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for phone in phones %}
                                    {% set current_owner = none %}
                                    {% for user in users %}
                                        {% if user.id == phone.current_owner_id %}
                                            {% set current_owner = user %}
                                        {% endif %}
                                    {% endfor %}
                                    <tr class="border-0">
                                        <td>
                                            <code class="text-muted">{{ phone.imei }}</code>
                                        </td>
                                        <td>
                                            <div class="fw-medium">{{ phone.brand }} {{ phone.model }}</div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ phone.color }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">{{ phone.project }}</span>
                                        </td>
                                        <td>
                            {% if phone.status == 'available' %}
                                <span class="badge bg-success-subtle text-success">可用</span>
                            {% elif phone.status == 'in_use' and phone.current_owner_id != phone.original_owner_id %}
                                <span class="badge bg-primary-subtle text-primary">使用中</span>
                            {% elif phone.status == 'in_use' and phone.current_owner_id == phone.original_owner_id %}
                                <span class="badge bg-success-subtle text-success">可用</span>
                            {% else %}
                                <span class="badge bg-secondary-subtle text-secondary">{{ phone.status }}</span>
                            {% endif %}
                        </td>
                                        <td>
                                            {% if current_owner %}
                                                <div>
                                                    <div class="fw-medium">{{ current_owner.name }}</div>
                                                    <small class="text-muted">{{ current_owner.department }}</small>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">未分配</span>
                                            {% endif %}
                                        </td>

                                        <td class="text-center">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ url_for('phone_detail', imei=phone.imei) }}" 
                                                   class="btn btn-outline-primary" title="查看详情">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ url_for('phone_trace', imei=phone.imei) }}" 
                                                   class="btn btn-outline-success" title="追溯">
                                                    <i class="bi bi-diagram-3"></i>
                                                </a>
                                                <a href="{{ url_for('edit_phone', imei=phone.imei) }}" 
                                                   class="btn btn-outline-warning" title="编辑">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-outline-danger" 
                                                        title="删除"
                                                        onclick="deletePhone('{{ phone.imei }}', '{{ phone.brand }} {{ phone.model }}')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <div class="empty-state">
                            <i class="bi bi-phone text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5 class="text-muted mb-3">
                                {% if search_params.keyword or search_params.status or search_params.user_id %}
                                    未找到匹配的手机
                                {% else %}
                                    暂无手机数据
                                {% endif %}
                            </h5>
                            <p class="text-muted mb-4">
                                {% if search_params.keyword or search_params.status or search_params.user_id %}
                                    请尝试调整搜索条件
                                {% else %}
                                    点击上方按钮添加第一台手机
                                {% endif %}
                            </p>
                            {% if not (search_params.keyword or search_params.status or search_params.user_id) %}
                                <a href="{{ url_for('add_phone') }}" class="btn btn-primary">
                                    <i class="bi bi-plus me-2"></i>添加手机
                                </a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除手机 <strong id="deletePhoneName"></strong> 吗？</p>
                <p class="text-danger small">注意：如果手机有活跃的借用记录，将无法删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let deletePhoneImei = null;
let searchTimeout = null;

// 实时搜索功能
function performSearch() {
    const formData = new FormData(document.getElementById('searchForm'));
    const params = new URLSearchParams(formData);
    
    // 显示加载状态
    const phoneListContainer = document.getElementById('phoneListContainer');
    const originalContent = phoneListContainer.innerHTML;
    phoneListContainer.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">搜索中...</span>
            </div>
            <p class="mt-3 text-muted">正在搜索手机...</p>
        </div>
    `;
    
    // 发起AJAX请求
    fetch('/api/phones/search?' + params.toString())
        .then(response => {
            if (!response.ok) {
                throw new Error('搜索请求失败');
            }
            return response.text();
        })
        .then(html => {
            // 更新手机列表内容
            phoneListContainer.innerHTML = html;
            
            // 更新URL
            const newUrl = window.location.pathname + '?' + params.toString();
            window.history.replaceState({}, '', newUrl);
            
            // 更新手机数量显示
            const phoneCount = document.querySelectorAll('tbody tr').length;
            const countBadge = document.querySelector('.badge.bg-secondary');
            if (countBadge) {
                countBadge.textContent = phoneCount;
            }
        })
        .catch(error => {
            console.error('搜索失败:', error);
            phoneListContainer.innerHTML = originalContent;
            showAlert('danger', '搜索失败，请重试');
        });
}

// 防抖搜索
function debouncedSearch() {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    searchTimeout = setTimeout(performSearch, 500); // 500ms延迟
}

// 清除筛选条件
function clearFilters() {
    document.getElementById('keyword').value = '';
    document.getElementById('project').value = '';
    document.getElementById('status').value = '';
    document.getElementById('user_id').value = '';
    performSearch();
}

// 绑定事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 关键词输入实时搜索
    document.getElementById('keyword').addEventListener('input', debouncedSearch);
    document.getElementById('project').addEventListener('input', debouncedSearch);
    
    // 下拉框变化立即搜索
    document.getElementById('status').addEventListener('change', performSearch);
    document.getElementById('user_id').addEventListener('change', performSearch);
});

function deletePhone(imei, phoneName) {
    deletePhoneImei = imei;
    document.getElementById('deletePhoneName').textContent = phoneName;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (deletePhoneImei) {
        fetch(`/phones/${deletePhoneImei}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('danger', data.message);
            }
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', '删除失败，请重试');
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        });
    }
});
</script>
{% endblock %}
