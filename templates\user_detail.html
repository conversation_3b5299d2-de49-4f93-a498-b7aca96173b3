{% extends "base.html" %}

{% block title %}{{ user.name }} - 用户详情 - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-person"></i> 用户详情
            </h1>
            <div>
                <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-warning me-2">
                    <i class="bi bi-pencil"></i> 编辑
                </a>
                <a href="{{ url_for('users') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回用户列表
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 用户基本信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i> 基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">用户ID:</th>
                                <td>{{ user.id }}</td>
                            </tr>
                            <tr>
                                <th>姓名:</th>
                                <td><strong>{{ user.name }}</strong></td>
                            </tr>
                            <tr>
                                <th>部门:</th>
                                <td>{{ user.department }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">邮箱:</th>
                                <td>
                                    <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                                </td>
                            </tr>
                            <tr>
                                <th>电话:</th>
                                <td>{{ user.phone_number or '-' }}</td>
                            </tr>
                            <tr>
                                <th>创建时间:</th>
                                <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '未设置' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart"></i> 统计信息
                </h5>
            </div>
            <div class="card-body">
                {% set current_phones = phones|selectattr('current_owner_id', 'equalto', user.id)|list %}
                {% set original_phones = phones|selectattr('original_owner_id', 'equalto', user.id)|list %}
                
                <div class="d-flex justify-content-between mb-2">
                    <span>当前持有手机:</span>
                    <span class="badge bg-primary">{{ current_phones|length }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>原始拥有手机:</span>
                    <span class="badge bg-info">{{ original_phones|length }}</span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>总关联手机:</span>
                    <span class="badge bg-secondary">{{ phones|length }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 关联手机列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-phone"></i> 关联手机
                    {% if phones %}
                        <span class="badge bg-secondary">{{ phones|length }}</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if phones %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>IMEI</th>
                                    <th>品牌型号</th>
                                    <th>颜色</th>
                                    <th>状态</th>
                                    <th>关系</th>
                                    <th>当前持有者</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for phone in phones %}
                                    {% set is_current_owner = phone.current_owner_id == user.id %}
                                    {% set is_original_owner = phone.original_owner_id == user.id %}
                                    <tr class="{% if is_current_owner %}table-success{% endif %}">
                                        <td><code>{{ phone.imei }}</code></td>
                                        <td><strong>{{ phone.brand }} {{ phone.model }}</strong></td>
                                        <td>{{ phone.color }}</td>
                                        <td>
                                            {% if phone.status == 'available' %}
                                                <span class="badge bg-success">可用</span>
                                            {% elif phone.status == 'in_use' %}
                                                <span class="badge bg-primary">使用中</span>
                                            {% elif phone.status == 'maintenance' %}
                                                <span class="badge bg-warning">维护中</span>
                                            {% elif phone.status == 'retired' %}
                                                <span class="badge bg-secondary">已退役</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if is_current_owner and is_original_owner %}
                                                <span class="badge bg-success">原始拥有者 + 当前持有者</span>
                                            {% elif is_current_owner %}
                                                <span class="badge bg-primary">当前持有者</span>
                                            {% elif is_original_owner %}
                                                <span class="badge bg-info">原始拥有者</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if phone.current_owner_id %}
                                                {% if phone.current_owner_id == user.id %}
                                                    <strong>{{ user.name }}</strong>
                                                {% else %}
                                                    {% set current_owner = phone.current_owner_id %}
                                                    <span class="text-muted">其他用户</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="text-muted">未分配</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('phone_detail', imei=phone.imei) }}" 
                                                   class="btn btn-sm btn-outline-info" title="查看详情">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="{{ url_for('phone_trace', imei=phone.imei) }}" 
                                                   class="btn btn-sm btn-outline-success" title="追溯">
                                                    <i class="bi bi-diagram-3"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-phone display-1 text-muted"></i>
                        <h4 class="mt-3 text-muted">暂无关联手机</h4>
                        <p class="text-muted">该用户还没有任何关联的手机</p>
                        <a href="{{ url_for('add_phone') }}" class="btn btn-primary">
                            <i class="bi bi-phone-vibrate"></i> 添加手机
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
