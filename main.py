#!/usr/bin/env python3
"""
手机管理系统 - 主程序入口
支持命令行界面和Web界面两种运行模式
"""

import sys
import argparse
from cli import CLI
from app import app


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description='手机管理系统')
    parser.add_argument('--mode', choices=['cli', 'web'], default='web',
                       help='运行模式: cli (命令行界面) 或 web (Web界面，默认)')
    parser.add_argument('--host', default='0.0.0.0',
                       help='Web服务器主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000,
                       help='Web服务器端口 (默认: 5000)')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'cli':
            print("启动命令行界面...")
            cli = CLI()
            cli.run()
        else:
            print(f"启动Web界面...")
            print(f"服务器地址: http://{args.host}:{args.port}")
            print("按 Ctrl+C 停止服务器")
            app.run(host=args.host, port=args.port, debug=args.debug)
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
