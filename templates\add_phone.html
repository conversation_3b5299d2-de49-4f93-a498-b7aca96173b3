{% extends "base.html" %}

{% block title %}添加手机 - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-phone-vibrate"></i> 添加手机
            </h1>
            <a href="{{ url_for('phones') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> 返回手机列表
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">手机信息</h5>
            </div>
            <div class="card-body">
                <form id="addPhoneForm">
                    <div class="mb-3">
                        <label for="imei" class="form-label">
                            IMEI编码 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="imei" name="imei" required>
                        <div class="form-text">手机的唯一标识符，通常为15位数字</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="brand" class="form-label">
                                    品牌 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="brand" name="brand" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="model" class="form-label">
                                    型号 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="model" name="model" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="color" class="form-label">
                            颜色 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="color" name="color" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="project" class="form-label">
                            所属项目 <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="project" name="project" required>
                        <div class="form-text">手机所属的项目名称</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="original_owner_id" class="form-label">
                            初始拥有者 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="original_owner_id" name="original_owner_id" required>
                            <option value="">请选择初始拥有者</option>
                            {% for user in users %}
                                <option value="{{ user.id }}">{{ user.name }} ({{ user.department }})</option>
                            {% endfor %}
                        </select>
                        {% if not users %}
                            <div class="form-text text-warning">
                                <i class="bi bi-exclamation-triangle"></i> 
                                暂无用户，请先 <a href="{{ url_for('add_user') }}">添加用户</a>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        <div class="form-text">可选项，记录手机的其他信息</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('phones') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary" {% if not users %}disabled{% endif %}>
                            <i class="bi bi-check-circle"></i> 添加手机
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('addPhoneForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 添加中...';
    submitBtn.disabled = true;
    
    fetch('/phones/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.href = '/phones';
            }, 1500);
        } else {
            showAlert('danger', data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '添加失败，请重试');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
