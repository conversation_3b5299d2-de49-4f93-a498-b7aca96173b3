{% extends "base.html" %}

{% block title %}新建借用 - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-plus-circle"></i> 新建借用
            </h1>
            <a href="{{ url_for('loans') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> 返回借用管理
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">借用信息</h5>
            </div>
            <div class="card-body">
                <form id="newLoanForm">
                    <div class="mb-3">
                        <label for="owner_id" class="form-label">
                            手机拥有者 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="owner_id" name="owner_id" required>
                            <option value="">请选择手机拥有者</option>
                            {% for user in users %}
                                <option value="{{ user.id }}">{{ user.name }} ({{ user.department }})</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">先选择拥有者，再选择其名下的手机</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone_imei" class="form-label">
                            选择手机 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="phone_imei" name="phone_imei" required disabled>
                            <option value="">请先选择手机拥有者</option>
                        </select>
                        <div class="form-text" id="phoneHelpText">选择拥有者后将显示其可借用的手机</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="borrower_id" class="form-label">
                            借用者 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="borrower_id" name="borrower_id" required>
                            <option value="">请选择借用者</option>
                            {% for user in users %}
                                <option value="{{ user.id }}">{{ user.name }} ({{ user.department }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="expected_return_date" class="form-label">预期归还日期</label>
                        <input type="date" class="form-control" id="expected_return_date" name="expected_return_date">
                        <div class="form-text">可选项，用于逾期提醒</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="purpose" class="form-label">借用目的</label>
                        <input type="text" class="form-control" id="purpose" name="purpose" 
                               placeholder="例如：出差使用、测试项目等">
                        <div class="form-text">可选项，记录借用原因</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">备注</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="其他需要说明的信息..."></textarea>
                    </div>
                    
                    <!-- 手机当前持有者信息显示 -->
                    <div id="phoneInfo" class="mb-3" style="display: none;">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle"></i> 手机信息
                            </h6>
                            <p class="mb-1"><strong>当前持有者：</strong><span id="currentOwner"></span></p>
                            <p class="mb-0"><strong>手机状态：</strong><span id="phoneStatus"></span></p>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('loans') }}" class="btn btn-secondary me-md-2">
                            <i class="bi bi-x-circle"></i> 取消
                        </a>
                        <button type="submit" class="btn btn-primary" {% if not users %}disabled{% endif %}>
                            <i class="bi bi-check-circle"></i> 创建借用
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局用户数据
const allUsers = {{ users|tojson }};

// 拥有者选择变化时更新手机列表
document.getElementById('owner_id').addEventListener('change', function() {
    const ownerId = this.value;
    const phoneSelect = document.getElementById('phone_imei');
    const borrowerSelect = document.getElementById('borrower_id');
    const phoneInfo = document.getElementById('phoneInfo');
    const phoneHelpText = document.getElementById('phoneHelpText');
    
    if (ownerId) {
        // 获取该用户拥有的可用手机
        console.log(`正在获取用户 ${ownerId} 的手机列表...`);
        fetch(`/api/users/${ownerId}/phones`)
            .then(response => {
                console.log('API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(phones => {
                console.log('获取到的手机列表:', phones);
                phoneSelect.innerHTML = '<option value="">请选择要借用的手机</option>';
                
                if (phones && phones.length > 0) {
                    phones.forEach(phone => {
                        const option = document.createElement('option');
                        option.value = phone.imei;
                        option.textContent = `${phone.brand} ${phone.model} (${phone.color}) - IMEI: ${phone.imei}`;
                        phoneSelect.appendChild(option);
                    });
                    phoneSelect.disabled = false;
                    phoneHelpText.textContent = `找到 ${phones.length} 台可借用的手机`;
                    phoneHelpText.className = 'form-text text-success';
                } else {
                    phoneSelect.innerHTML = '<option value="">该用户暂无可借用的手机</option>';
                    phoneSelect.disabled = true;
                    phoneHelpText.textContent = '该用户没有状态为"可用"的手机';
                    phoneHelpText.className = 'form-text text-warning';
                }
                
                // 更新借用者下拉框，排除当前拥有者
                updateBorrowerSelect(borrowerSelect, parseInt(ownerId));
            })
            .catch(error => {
                console.error('获取手机列表失败:', error);
                showAlert('danger', '获取手机列表失败: ' + error.message);
                phoneSelect.innerHTML = '<option value="">加载失败，请重试</option>';
                phoneSelect.disabled = true;
                phoneHelpText.textContent = '加载失败，请重试';
                phoneHelpText.className = 'form-text text-danger';
            });
    } else {
        phoneSelect.innerHTML = '<option value="">请先选择手机拥有者</option>';
        phoneSelect.disabled = true;
        phoneHelpText.textContent = '选择拥有者后将显示其可借用的手机';
        phoneHelpText.className = 'form-text text-muted';
        phoneInfo.style.display = 'none';
        
        // 恢复借用者下拉框
        updateBorrowerSelect(borrowerSelect, null);
    }
});

// 更新借用者下拉框的函数
function updateBorrowerSelect(borrowerSelect, excludeUserId) {
    borrowerSelect.innerHTML = '<option value="">请选择借用者</option>';
    
    allUsers.forEach(user => {
        if (!excludeUserId || user.id !== excludeUserId) {
            const option = document.createElement('option');
            option.value = user.id;
            option.textContent = `${user.name} (${user.department})`;
            borrowerSelect.appendChild(option);
        }
    });
}

// 手机选择变化时显示手机信息
document.getElementById('phone_imei').addEventListener('change', function() {
    const phoneImei = this.value;
    const phoneInfo = document.getElementById('phoneInfo');
    const ownerId = document.getElementById('owner_id').value;
    
    if (phoneImei && ownerId) {
        // 显示手机信息
        phoneInfo.style.display = 'block';
        
        // 获取拥有者信息
        const ownerSelect = document.getElementById('owner_id');
        const ownerText = ownerSelect.options[ownerSelect.selectedIndex].text;
        document.getElementById('currentOwner').textContent = ownerText;
        document.getElementById('phoneStatus').textContent = '可用';
    } else {
        phoneInfo.style.display = 'none';
    }
});

// 设置预期归还日期的最小值为明天
document.addEventListener('DOMContentLoaded', function() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    document.getElementById('expected_return_date').min = tomorrowStr;
});

document.getElementById('newLoanForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // 显示加载状态
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建中...';
    submitBtn.disabled = true;
    
    fetch('/loans/new', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.href = '/loans';
            }, 1500);
        } else {
            showAlert('danger', data.message);
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', '创建失败，请重试');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
