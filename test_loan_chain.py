#!/usr/bin/env python3
"""
测试借用链功能
"""

from phone_manager import PhoneManager
from datetime import datetime

def test_loan_chain():
    """测试借用链功能"""
    manager = PhoneManager()
    
    print("=== 借用链功能测试 ===")
    
    # 假设我们有一个手机和用户
    phones = manager.list_phones()
    users = manager.list_users()
    
    if not phones or len(users) < 3:
        print("测试需要至少1台手机和3个用户")
        return
    
    phone = phones[0]
    user_a, user_b, user_c = users[0], users[1], users[2]
    
    print(f"测试手机: {phone.brand} {phone.model} (IMEI: {phone.imei})")
    print(f"用户A（原始拥有者）: {user_a.name}")
    print(f"用户B: {user_b.name}")
    print(f"用户C: {user_c.name}")
    print(f"手机初始状态: {phone.status}")
    print(f"手机当前持有者ID: {phone.current_owner_id}")
    print()
    
    # 场景：A借给B，B又借给C，然后B尝试直接还给A
    print("1. 模拟A借给B...")
    success, msg = manager.loan_phone(phone.imei, user_b.id, user_a.id, purpose="测试借用1")
    print(f"   结果: {msg}")
    print(f"   成功: {success}")
    
    if not success:
        print("第一步借用失败，无法继续测试")
        return
    
    print("\n2. 模拟B尝试借给C（应该被阻止）...")
    success, msg = manager.loan_phone(phone.imei, user_c.id, user_b.id, purpose="测试借用2")
    print(f"   结果: {msg}")
    print(f"   成功: {success}")
    
    if success:
        print("   ⚠️ 错误！B不应该能够再次借出手机")
        
        print("\n3. 查看借用链...")
        success, message, chain_info = manager.get_loan_chain_info(phone.imei)
        if success:
            print(f"   {message}")
            for i, loan in enumerate(chain_info):
                status = "（当前持有者）" if loan['is_current_holder'] else ""
                print(f"   第{loan['step']}步: {loan['lender_name']} → {loan['borrower_name']} {status}")
        
        print("\n4. 模拟B尝试直接还给A（应该被阻止）...")
        # 获取B的借用记录ID
        active_loans = manager.get_active_loans(phone.imei)
        b_loan_record = None
        for loan in active_loans:
            if loan.borrower_id == user_b.id:
                b_loan_record = loan
                break
        
        if b_loan_record:
            success, msg = manager.return_phone(b_loan_record.id, user_b.id)
            print(f"   结果: {msg}")
            print(f"   成功: {success}")
        else:
            print("   找不到B的借用记录")
        
        print("\n5. 模拟C先还给B（正确的顺序）...")
        # 获取C的借用记录ID
        c_loan_record = None
        for loan in active_loans:
            if loan.borrower_id == user_c.id:
                c_loan_record = loan
                break
        
        if c_loan_record:
            success, msg = manager.return_phone(c_loan_record.id, user_c.id)
            print(f"   结果: {msg}")
            print(f"   成功: {success}")
            
            if success:
                print("\n6. 现在B可以还给A了...")
                success, msg = manager.return_phone(b_loan_record.id, user_b.id)
                print(f"   结果: {msg}")
                print(f"   成功: {success}")
    else:
        print("   ✅ 正确！B被正确阻止了再次借出手机")
        
        print("\n3. 现在测试B归还给A...")
        active_loans = manager.get_active_loans(phone.imei)
        if active_loans:
            loan_record = active_loans[0]  # 只有一个活跃的借用记录
            success, msg = manager.return_phone(loan_record.id, user_b.id)
            print(f"   结果: {msg}")
            print(f"   成功: {success}")
        
    print("\n=== 测试完成 ===")

def test_chain_scenario():
    """测试完整的借用链场景"""
    print("\n\n=== 完整借用链场景测试 ===")
    
    manager = PhoneManager()
    phones = manager.list_phones()
    users = manager.list_users()
    
    if not phones or len(users) < 3:
        print("测试需要至少1台手机和3个用户")
        return
    
    # 选择一个可用的手机
    available_phone = None
    for phone in phones:
        if phone.status == 'available':
            available_phone = phone
            break
    
    if not available_phone:
        print("没有可用的手机")
        return
        
    user_a, user_b, user_c = users[0], users[1], users[2]
    
    print(f"测试手机: {available_phone.brand} {available_phone.model}")
    print(f"A: {user_a.name}, B: {user_b.name}, C: {user_c.name}")
    
    # 1. A借给B
    print("\n1. A借给B...")
    success, msg = manager.loan_phone(available_phone.imei, user_b.id, user_a.id)
    print(f"   {msg} (成功: {success})")
    
    if success:
        # 2. B尝试借给C (应该失败)
        print("\n2. B尝试借给C (应该失败)...")
        success2, msg2 = manager.loan_phone(available_phone.imei, user_c.id, user_b.id)
        print(f"   {msg2} (成功: {success2})")
        
        if not success2:
            print("   ✅ 正确！系统正确阻止了这个操作")
        else:
            print("   ⚠️ 错误！系统允许了不应该允许的操作")
        
        # 3. B归还给A
        print("\n3. B归还给A...")
        active_loans = manager.get_active_loans(available_phone.imei)
        if active_loans:
            loan = active_loans[0]
            success3, msg3 = manager.return_phone(loan.id, user_b.id)
            print(f"   {msg3} (成功: {success3})")

if __name__ == "__main__":
    test_loan_chain()
    test_chain_scenario()