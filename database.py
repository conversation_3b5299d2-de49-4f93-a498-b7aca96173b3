"""
手机管理系统 - 数据库管理
使用SQLite数据库存储数据
"""

import sqlite3
from datetime import datetime
from typing import List, Optional
from models import User, Phone, PhoneLoanRecord, PhoneHistory


class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, db_path: str = "phone_manager.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = self.get_connection()
        try:
            # 创建用户表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    department TEXT NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    phone_number TEXT,
                    password_hash TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'user',
                    created_at TEXT NOT NULL
                )
            """)
            
            # 创建手机表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS phones (
                    imei TEXT PRIMARY KEY,
                    brand TEXT NOT NULL,
                    model TEXT NOT NULL,
                    color TEXT,
                    project TEXT NOT NULL,
                    original_owner_id INTEGER NOT NULL,
                    current_owner_id INTEGER,
                    status TEXT DEFAULT 'available',
                    notes TEXT DEFAULT '',
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (original_owner_id) REFERENCES users (id),
                    FOREIGN KEY (current_owner_id) REFERENCES users (id)
                )
            """)
            
            # 创建借用记录表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS loan_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    phone_imei TEXT NOT NULL,
                    borrower_id INTEGER NOT NULL,
                    lender_id INTEGER NOT NULL,
                    loan_date TEXT NOT NULL,
                    expected_return_date TEXT,
                    actual_return_date TEXT,
                    purpose TEXT DEFAULT '',
                    status TEXT DEFAULT 'active',
                    notes TEXT DEFAULT '',
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (phone_imei) REFERENCES phones (imei),
                    FOREIGN KEY (borrower_id) REFERENCES users (id),
                    FOREIGN KEY (lender_id) REFERENCES users (id)
                )
            """)
            
            # 创建历史记录表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS phone_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    phone_imei TEXT NOT NULL,
                    action TEXT NOT NULL,
                    from_user_id INTEGER,
                    to_user_id INTEGER,
                    timestamp TEXT NOT NULL,
                    description TEXT DEFAULT '',
                    FOREIGN KEY (phone_imei) REFERENCES phones (imei),
                    FOREIGN KEY (from_user_id) REFERENCES users (id),
                    FOREIGN KEY (to_user_id) REFERENCES users (id)
                )
            """)
            
            conn.commit()
        finally:
            conn.close()
    
    # 用户相关操作
    def add_user(self, user: User) -> int:
        """添加用户"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                INSERT INTO users (name, department, email, phone_number, password_hash, role, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (user.name, user.department, user.email, user.phone_number, 
                  user.password_hash, user.role, user.created_at.isoformat()))
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()

    def get_user_by_email(self, email: str) -> Optional[User]:
        """根据Email获取用户"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM users WHERE email = ?", (email,))
            row = cursor.fetchone()
            if row:
                return User(
                    id=row['id'],
                    name=row['name'],
                    department=row['department'],
                    email=row['email'],
                    phone_number=row['phone_number'],
                    password_hash=row['password_hash'],
                    role=row['role'],
                    created_at=datetime.fromisoformat(row['created_at'])
                )
            return None
        finally:
            conn.close()

    def get_user(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            row = cursor.fetchone()
            if row:
                return User(
                    id=row['id'],
                    name=row['name'],
                    department=row['department'],
                    email=row['email'],
                    phone_number=row['phone_number'],
                    password_hash=row['password_hash'],
                    role=row['role'],
                    created_at=datetime.fromisoformat(row['created_at'])
                )
            return None
        finally:
            conn.close()
    
    def get_all_users(self) -> List[User]:
        """获取所有用户"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM users ORDER BY name")
            users = []
            for row in cursor.fetchall():
                users.append(User(
                    id=row['id'],
                    name=row['name'],
                    department=row['department'],
                    email=row['email'],
                    phone_number=row['phone_number'],
                    password_hash=row['password_hash'],
                    role=row['role'],
                    created_at=datetime.fromisoformat(row['created_at'])
                ))
            return users
        finally:
            conn.close()
    
    def update_user(self, user: User) -> bool:
        """更新用户信息 (不包括密码)"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                UPDATE users SET name = ?, department = ?, email = ?, phone_number = ?, role = ?
                WHERE id = ?
            """, (user.name, user.department, user.email, user.phone_number, user.role, user.id))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()
    
    def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("DELETE FROM users WHERE id = ?", (user_id,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()
    
    # ... (The rest of the file remains the same) ...
    def add_phone(self, phone: Phone) -> bool:
        """添加手机"""
        conn = self.get_connection()
        try:
            conn.execute("""
                INSERT INTO phones (imei, brand, model, color, project,
                                  original_owner_id, current_owner_id, status, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (phone.imei, phone.brand, phone.model, phone.color, phone.project,
                  phone.original_owner_id, phone.current_owner_id, phone.status, phone.notes,
                  phone.created_at.isoformat()))
            conn.commit()
            
            # 添加历史记录
            self._add_history_record(phone.imei, "created", None, phone.original_owner_id,
                                   f"手机入库，初始拥有者: {self.get_user(phone.original_owner_id).name}")
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def get_phone(self, imei: str) -> Optional[Phone]:
        """根据IMEI获取手机"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM phones WHERE imei = ?", (imei,))
            row = cursor.fetchone()
            if row:
                return Phone(
                    imei=row['imei'],
                    brand=row['brand'],
                    model=row['model'],
                    color=row['color'],
                    project=row['project'],
                    original_owner_id=row['original_owner_id'],
                    current_owner_id=row['current_owner_id'],
                    status=row['status'],
                    notes=row['notes'],
                    created_at=datetime.fromisoformat(row['created_at'])
                )
            return None
        finally:
            conn.close()
    
    def get_all_phones(self) -> List[Phone]:
        """获取所有手机"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM phones ORDER BY brand, model")
            phones = []
            for row in cursor.fetchall():
                phones.append(Phone(
                    imei=row['imei'],
                    brand=row['brand'],
                    model=row['model'],
                    color=row['color'],
                    project=row['project'],
                    original_owner_id=row['original_owner_id'],
                    current_owner_id=row['current_owner_id'],
                    status=row['status'],
                    notes=row['notes'],
                    created_at=datetime.fromisoformat(row['created_at'])
                ))
            return phones
        finally:
            conn.close()
    
    def update_phone(self, phone: Phone) -> bool:
        """更新手机信息"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                UPDATE phones SET brand = ?, model = ?, color = ?, project = ?,
                                current_owner_id = ?, status = ?, notes = ?
                WHERE imei = ?
            """, (phone.brand, phone.model, phone.color, phone.project,
                  phone.current_owner_id, phone.status, phone.notes, phone.imei))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()
    
    def delete_phone(self, imei: str) -> bool:
        """删除手机"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("DELETE FROM phones WHERE imei = ?", (imei,))
            conn.commit()
            return cursor.rowcount > 0
        finally:
            conn.close()
    
    def get_phones_by_user(self, user_id: int, current_only: bool = True) -> List[Phone]:
        """获取用户的手机"""
        conn = self.get_connection()
        try:
            if current_only:
                cursor = conn.execute("SELECT * FROM phones WHERE current_owner_id = ?", (user_id,))
            else:
                cursor = conn.execute("""
                    SELECT * FROM phones WHERE original_owner_id = ? OR current_owner_id = ?
                """, (user_id, user_id))
            
            phones = []
            for row in cursor.fetchall():
                phones.append(Phone(
                    imei=row['imei'],
                    brand=row['brand'],
                    model=row['model'],
                    color=row['color'],
                    project=row['project'],
                    original_owner_id=row['original_owner_id'],
                    current_owner_id=row['current_owner_id'],
                    status=row['status'],
                    notes=row['notes'],
                    created_at=datetime.fromisoformat(row['created_at'])
                ))
            return phones
        finally:
            conn.close()
    
    # 借用记录相关操作
    def add_loan_record(self, record: PhoneLoanRecord) -> int:
        """添加借用记录"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                INSERT INTO loan_records (phone_imei, borrower_id, lender_id, loan_date,
                                        expected_return_date, purpose, status, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (record.phone_imei, record.borrower_id, record.lender_id,
                  record.loan_date.isoformat(),
                  record.expected_return_date.isoformat() if record.expected_return_date else None,
                  record.purpose, record.status, record.notes, record.created_at.isoformat()))
            conn.commit()
            
            # 更新手机当前持有者
            conn.execute("UPDATE phones SET current_owner_id = ?, status = 'in_use' WHERE imei = ?",
                        (record.borrower_id, record.phone_imei))
            conn.commit()
            
            # 添加历史记录
            borrower = self.get_user(record.borrower_id)
            lender = self.get_user(record.lender_id)
            self._add_history_record(record.phone_imei, "transferred", record.lender_id, record.borrower_id,
                                   f"从 {lender.name} 借给 {borrower.name}")
            
            return cursor.lastrowid
        finally:
            conn.close()

    def get_loan_record(self, record_id: int) -> Optional[PhoneLoanRecord]:
        """根据ID获取借用记录"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("SELECT * FROM loan_records WHERE id = ?", (record_id,))
            row = cursor.fetchone()
            if row:
                return PhoneLoanRecord(
                    id=row['id'],
                    phone_imei=row['phone_imei'],
                    borrower_id=row['borrower_id'],
                    lender_id=row['lender_id'],
                    loan_date=datetime.fromisoformat(row['loan_date']),
                    expected_return_date=datetime.fromisoformat(row['expected_return_date']) if row['expected_return_date'] else None,
                    actual_return_date=datetime.fromisoformat(row['actual_return_date']) if row['actual_return_date'] else None,
                    purpose=row['purpose'],
                    status=row['status'],
                    notes=row['notes'],
                    created_at=datetime.fromisoformat(row['created_at'])
                )
            return None
        finally:
            conn.close()
    
    def return_phone(self, record_id: int, return_date: datetime = None) -> bool:
        """归还手机"""
        if return_date is None:
            return_date = datetime.now()
        
        conn = self.get_connection()
        try:
            # 更新借用记录
            cursor = conn.execute("""
                UPDATE loan_records SET actual_return_date = ?, status = 'returned'
                WHERE id = ? AND status = 'active'
            """, (return_date.isoformat(), record_id))
            
            if cursor.rowcount == 0:
                return False
            
            # 获取借用记录信息
            cursor = conn.execute("SELECT * FROM loan_records WHERE id = ?", (record_id,))
            record = cursor.fetchone()
            
            # 更新手机当前持有者为出借者
            conn.execute("UPDATE phones SET current_owner_id = ?, status = 'available' WHERE imei = ?",
                        (record['lender_id'], record['phone_imei']))
            
            conn.commit()
            
            # 添加历史记录
            borrower = self.get_user(record['borrower_id'])
            lender = self.get_user(record['lender_id'])
            self._add_history_record(record['phone_imei'], "returned", record['borrower_id'], record['lender_id'],
                                   f"{borrower.name} 归还给 {lender.name}")
            
            return True
        finally:
            conn.close()
    
    def get_active_loan_records(self, phone_imei: str = None) -> List[PhoneLoanRecord]:
        """获取活跃的借用记录"""
        conn = self.get_connection()
        try:
            if phone_imei:
                cursor = conn.execute("""
                    SELECT * FROM loan_records WHERE phone_imei = ? AND status = 'active'
                    ORDER BY loan_date DESC
                """, (phone_imei,))
            else:
                cursor = conn.execute("""
                    SELECT * FROM loan_records WHERE status = 'active'
                    ORDER BY loan_date DESC
                """)
            
            records = []
            for row in cursor.fetchall():
                records.append(PhoneLoanRecord(
                    id=row['id'],
                    phone_imei=row['phone_imei'],
                    borrower_id=row['borrower_id'],
                    lender_id=row['lender_id'],
                    loan_date=datetime.fromisoformat(row['loan_date']),
                    expected_return_date=datetime.fromisoformat(row['expected_return_date']) if row['expected_return_date'] else None,
                    actual_return_date=datetime.fromisoformat(row['actual_return_date']) if row['actual_return_date'] else None,
                    purpose=row['purpose'],
                    status=row['status'],
                    notes=row['notes'],
                    created_at=datetime.fromisoformat(row['created_at'])
                ))
            return records
        finally:
            conn.close()
    
    def get_loan_chain(self, phone_imei: str) -> List[PhoneLoanRecord]:
        """获取手机的借用链（按借用时间顺序）"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                SELECT * FROM loan_records WHERE phone_imei = ? AND status = 'active'
                ORDER BY loan_date ASC
            """, (phone_imei,))
            
            records = []
            for row in cursor.fetchall():
                records.append(PhoneLoanRecord(
                    id=row['id'],
                    phone_imei=row['phone_imei'],
                    borrower_id=row['borrower_id'],
                    lender_id=row['lender_id'],
                    loan_date=datetime.fromisoformat(row['loan_date']),
                    expected_return_date=datetime.fromisoformat(row['expected_return_date']) if row['expected_return_date'] else None,
                    actual_return_date=datetime.fromisoformat(row['actual_return_date']) if row['actual_return_date'] else None,
                    purpose=row['purpose'],
                    status=row['status'],
                    notes=row['notes'],
                    created_at=datetime.fromisoformat(row['created_at'])
                ))
            return records
        finally:
            conn.close()
    
    def get_phone_history(self, phone_imei: str) -> List[PhoneHistory]:
        """获取手机历史记录"""
        conn = self.get_connection()
        try:
            cursor = conn.execute("""
                SELECT * FROM phone_history WHERE phone_imei = ?
                ORDER BY timestamp DESC
            """, (phone_imei,))
            
            history = []
            for row in cursor.fetchall():
                history.append(PhoneHistory(
                    id=row['id'],
                    phone_imei=row['phone_imei'],
                    action=row['action'],
                    from_user_id=row['from_user_id'],
                    to_user_id=row['to_user_id'],
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    description=row['description']
                ))
            return history
        finally:
            conn.close()
    
    def _add_history_record(self, phone_imei: str, action: str, from_user_id: int = None,
                           to_user_id: int = None, description: str = ""):
        """添加历史记录"""
        conn = self.get_connection()
        try:
            conn.execute("""
                INSERT INTO phone_history (phone_imei, action, from_user_id, to_user_id, timestamp, description)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (phone_imei, action, from_user_id, to_user_id, datetime.now().isoformat(), description))
            conn.commit()
        finally:
            conn.close()