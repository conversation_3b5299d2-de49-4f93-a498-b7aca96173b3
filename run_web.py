#!/usr/bin/env python3
"""
手机管理系统 - Web界面快速启动脚本
"""

from app import app

if __name__ == '__main__':
    print("=" * 60)
    print("  手机管理系统 - Web界面")
    print("=" * 60)
    print("服务器启动中...")
    print("访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("请检查端口5000是否被占用，或尝试运行: python main.py --mode web --port 8080")
