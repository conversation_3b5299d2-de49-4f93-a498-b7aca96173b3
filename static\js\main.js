// 手机管理系统 - 主JavaScript文件

// 全局变量
let toastContainer;

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeToastContainer();
    initializeTooltips();
    initializeDatepickers();
    initializeFormValidation();
});

// 初始化Toast容器
function initializeToastContainer() {
    toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '1055';
    document.body.appendChild(toastContainer);
}

// 显示警告消息
function showAlert(type, message, duration = 5000) {
    const alertId = 'alert-' + Date.now();
    const alertHtml = `
        <div class="toast align-items-center border-0 shadow" role="alert" id="${alertId}">
            <div class="d-flex">
                <div class="toast-body text-${type === 'danger' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'}">
                    <i class="bi bi-${getAlertIcon(type)} me-2"></i>${message}
                </div>
                <button type="button" class="btn-close me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    const toastElement = document.getElementById(alertId);
    const toast = new bootstrap.Toast(toastElement, {
        delay: duration
    });
    
    toast.show();
    
    // 自动移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

// 获取警告图标
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle',
        'primary': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 初始化工具提示
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化日期选择器
function initializeDatepickers() {
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        // 设置默认的最小日期为今天
        if (input.name === 'expected_return_date') {
            const today = new Date().toISOString().split('T')[0];
            input.min = today;
        }
    });
}

// 初始化表单验证
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 获取状态徽章HTML
function getStatusBadge(status) {
    const badges = {
        'available': '<span class="badge bg-success">可用</span>',
        'in_use': '<span class="badge bg-primary">使用中</span>',
        'maintenance': '<span class="badge bg-warning text-dark">维护中</span>',
        'retired': '<span class="badge bg-secondary">已退役</span>'
    };
    return badges[status] || `<span class="badge bg-light text-dark">${status}</span>`;
}

// 确认对话框
function confirmDialog(title, message, callback) {
    const modalId = 'confirmModal-' + Date.now();
    const modalHtml = `
        <div class="modal fade" id="${modalId}" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-danger" id="confirmBtn-${modalId}">确认</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    const modalElement = document.getElementById(modalId);
    const modal = new bootstrap.Modal(modalElement);
    
    document.getElementById(`confirmBtn-${modalId}`).addEventListener('click', function() {
        callback();
        modal.hide();
    });
    
    modalElement.addEventListener('hidden.bs.modal', function() {
        modalElement.remove();
    });
    
    modal.show();
}

// 加载状态管理
function setLoading(element, loading = true) {
    if (loading) {
        element.classList.add('loading');
        element.disabled = true;
    } else {
        element.classList.remove('loading');
        element.disabled = false;
    }
}

// AJAX请求包装器
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, mergedOptions);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || '请求失败');
        }
        
        return data;
    } catch (error) {
        console.error('API请求错误:', error);
        throw error;
    }
}

// 表格排序功能
function initializeTableSort() {
    const sortableHeaders = document.querySelectorAll('th[data-sort]');
    
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.innerHTML += ' <i class="bi bi-arrow-down-up text-muted"></i>';
        
        header.addEventListener('click', function() {
            const table = this.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const column = this.dataset.sort;
            const columnIndex = Array.from(this.parentNode.children).indexOf(this);
            
            // 获取当前排序状态
            const currentSort = this.dataset.sortDirection || 'asc';
            const newSort = currentSort === 'asc' ? 'desc' : 'asc';
            
            // 清除其他列的排序状态
            sortableHeaders.forEach(h => {
                h.dataset.sortDirection = '';
                const icon = h.querySelector('i');
                icon.className = 'bi bi-arrow-down-up text-muted';
            });
            
            // 设置当前列的排序状态
            this.dataset.sortDirection = newSort;
            const icon = this.querySelector('i');
            icon.className = `bi bi-arrow-${newSort === 'asc' ? 'up' : 'down'} text-primary`;
            
            // 排序行
            rows.sort((a, b) => {
                const aValue = a.children[columnIndex].textContent.trim();
                const bValue = b.children[columnIndex].textContent.trim();
                
                let comparison = 0;
                if (aValue > bValue) comparison = 1;
                if (aValue < bValue) comparison = -1;
                
                return newSort === 'asc' ? comparison : -comparison;
            });
            
            // 重新插入排序后的行
            rows.forEach(row => tbody.appendChild(row));
        });
    });
}

// 搜索高亮功能
function highlightSearchTerm(text, term) {
    if (!term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<span class="search-highlight">$1</span>');
}

// 复制到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showAlert('success', '已复制到剪贴板');
    } catch (err) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('success', '已复制到剪贴板');
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出功能
function exportTableToCSV(tableId, filename = 'export.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    let csv = [];
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');
        
        for (let j = 0; j < cols.length - 1; j++) { // 排除操作列
            let data = cols[j].textContent.trim();
            data = data.replace(/"/g, '""'); // 转义双引号
            row.push('"' + data + '"');
        }
        
        csv.push(row.join(','));
    }
    
    const csvContent = csv.join('\n');
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 打印功能
function printPage() {
    window.print();
}

// 返回顶部功能
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 检查是否需要显示返回顶部按钮
function checkScrollTop() {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const backToTopBtn = document.getElementById('backToTop');
    
    if (backToTopBtn) {
        if (scrollTop > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    }
}

// 监听滚动事件
window.addEventListener('scroll', throttle(checkScrollTop, 100));

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('全局错误:', e.error);
    showAlert('danger', '发生了一个错误，请刷新页面重试');
});

// 网络状态监测
window.addEventListener('online', function() {
    showAlert('success', '网络连接已恢复');
});

window.addEventListener('offline', function() {
    showAlert('warning', '网络连接已断开，某些功能可能不可用');
});

// 导出全局函数
window.PhoneManager = {
    showAlert,
    confirmDialog,
    setLoading,
    apiRequest,
    formatDate,
    formatDateTime,
    getStatusBadge,
    copyToClipboard,
    exportTableToCSV,
    printPage,
    scrollToTop,
    debounce,
    throttle
};
