# 手机管理系统 - 快速上手指南

## 🚀 快速启动

### 方法一：使用演示数据（推荐）

1. **创建演示数据**
   ```bash
   python demo.py
   ```

2. **启动Web界面**
   ```bash
   python run_web.py
   ```

3. **访问系统**
   - 打开浏览器访问：http://localhost:5000
   - 系统已包含完整的演示数据

### 方法二：从空白开始

1. **启动Web界面**
   ```bash
   python run_web.py
   ```

2. **访问系统并添加数据**
   - 访问：http://localhost:5000
   - 首先添加用户
   - 然后添加手机
   - 最后创建借用记录

### 方法三：使用命令行界面

```bash
python run_cli.py
```

## 📱 系统功能概览

### 🏠 首页
- 系统统计概览
- 逾期借用提醒
- 快捷操作入口

### 👥 用户管理
- ➕ 添加用户（姓名、部门、邮箱、电话）
- 📋 用户列表查看
- 👤 用户详情页面
- ✏️ 编辑用户信息
- 🗑️ 删除用户（检查关联手机）

### 📱 手机管理
- ➕ 添加手机（IMEI、品牌、型号、颜色等）
- 📋 手机列表查看
- 🔍 搜索和筛选功能
- 📱 手机详情页面
- ✏️ 编辑手机信息
- 🗑️ 删除手机（检查借用记录）
- 🔄 手机追溯功能

### 🔄 借用管理
- ➕ 创建借用记录
- 📋 活跃借用列表
- ⚠️ 逾期借用提醒
- ✅ 手机归还处理

### 📊 追溯功能
- 📈 完整的手机历史轨迹
- 👥 所有权变更记录
- 🔄 借用归还历史
- 📝 操作日志查看

## 🎯 演示数据说明

系统包含以下演示数据：

### 👥 用户（5个）
- 张三（技术部）- 拥有2台手机
- 李四（产品部）- 拥有1台手机，借用1台（逾期）
- 王五（设计部）- 拥有1台手机，借用1台
- 赵六（测试部）- 拥有1台手机
- 钱七（运营部）- 拥有1台手机，借用1台

### 📱 手机（6台）
- iPhone 14 Pro（张三 → 王五借用中）
- Galaxy S23（李四 → 钱七借用中）
- Mate 50（王五自用）
- 13 Pro（张三自用）
- Find X5（赵六 → 李四借用中，已逾期）
- X90（钱七自用）

### 🔄 借用记录（3条）
- 1条正常借用（iPhone → 王五）
- 1条正常借用（Galaxy → 钱七）
- 1条逾期借用（Find X5 → 李四）

## 💡 使用技巧

### 🔍 搜索功能
- 在手机管理页面可以按品牌、型号、IMEI、颜色搜索
- 可以按状态和用户筛选
- 支持组合搜索条件

### 📊 状态管理
- **可用**：可以被借用
- **使用中**：已被借用
- **维护中**：暂时不可用
- **已退役**：不再使用

### ⚠️ 注意事项
- IMEI编码必须唯一，不可重复
- 删除用户前需要处理关联的手机
- 删除手机前需要处理活跃的借用记录
- 逾期借用会在首页显示提醒

### 🔄 借用流程
1. 选择状态为"可用"的手机
2. 选择借用者（不能是当前持有者）
3. 设置预期归还日期（可选）
4. 填写借用目的和备注
5. 系统自动更新手机状态和持有者

### 📈 追溯查看
- 每台手机都有完整的历史记录
- 可以查看所有权变更历史
- 借用归还轨迹一目了然
- 支持按时间倒序查看

## 🛠️ 开发模式

启动开发模式（自动重载）：
```bash
python main.py --mode web --debug
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.7+
2. 依赖包是否正确安装
3. 端口5000是否被占用
4. 数据库文件权限是否正确

## 🎉 开始使用

现在您可以开始使用手机管理系统了！建议先运行演示数据脚本来体验完整功能。
