#!/usr/bin/env python3
"""
手机管理系统 - 功能测试脚本
"""

import os
from datetime import datetime
from phone_manager import PhoneManager

def test_system():
    """测试系统核心功能"""
    print("=" * 60)
    print("  手机管理系统功能测试")
    print("=" * 60)
    
    # 初始化管理器
    manager = PhoneManager("test_phone_manager.db")
    
    try:
        # 测试1: 创建用户
        print("\n1. 测试用户管理功能...")
        success, msg = manager.create_user("张三", "技术部", "<EMAIL>", "13800138000")
        print(f"   创建用户: {'成功' if success else '失败'} - {msg}")
        
        success, msg = manager.create_user("李四", "产品部", "<EMAIL>", "13800138001")
        print(f"   创建用户: {'成功' if success else '失败'} - {msg}")
        
        # 测试2: 添加手机
        print("\n2. 测试手机管理功能...")
        success, msg = manager.add_phone(
            "123456789012345", "Apple", "iPhone 14", "深空灰", 
            datetime(2024, 1, 15), 1, "公司配发手机"
        )
        print(f"   添加手机: {'成功' if success else '失败'} - {msg}")
        
        success, msg = manager.add_phone(
            "123456789012346", "Samsung", "Galaxy S23", "幻影黑", 
            datetime(2024, 2, 10), 2, "测试用机"
        )
        print(f"   添加手机: {'成功' if success else '失败'} - {msg}")
        
        # 测试3: 借用功能
        print("\n3. 测试借用管理功能...")
        success, msg = manager.loan_phone(
            "123456789012345", 2, datetime(2024, 12, 31), "项目测试使用"
        )
        print(f"   借用手机: {'成功' if success else '失败'} - {msg}")
        
        # 测试4: 查询功能
        print("\n4. 测试查询功能...")
        users = manager.list_users()
        print(f"   用户总数: {len(users)}")
        
        phones = manager.list_phones()
        print(f"   手机总数: {len(phones)}")
        
        active_loans = manager.get_active_loans()
        print(f"   活跃借用: {len(active_loans)}")
        
        # 测试5: 追溯功能
        print("\n5. 测试追溯功能...")
        success, trace_info = manager.get_phone_trace("123456789012345")
        if success:
            print("   手机追溯信息获取成功")
            print("   " + "="*50)
            for line in trace_info.split('\n')[:10]:  # 只显示前10行
                if line.strip():
                    print("   " + line)
            print("   ...")
        else:
            print(f"   追溯失败: {trace_info}")
        
        # 测试6: 统计功能
        print("\n6. 测试统计功能...")
        stats = manager.get_statistics()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        print("\n" + "=" * 60)
        print("  所有测试完成！系统功能正常")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        return False
    
    finally:
        # 清理测试数据库
        if os.path.exists("test_phone_manager.db"):
            os.remove("test_phone_manager.db")
            print("\n测试数据库已清理")

if __name__ == '__main__':
    test_system()
