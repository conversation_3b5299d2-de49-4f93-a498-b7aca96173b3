"""
手机管理系统 - 命令行界面
提供用户友好的交互界面
"""

import os
import sys
from datetime import datetime, timedelta
from typing import List, Optional
from phone_manager import PhoneManager
from models import User, Phone


class CLI:
    """命令行界面类"""
    
    def __init__(self):
        self.manager = PhoneManager()
        self.running = True
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_header(self, title: str):
        """打印标题头"""
        print("=" * 60)
        print(f"  {title}")
        print("=" * 60)
    
    def print_separator(self):
        """打印分隔符"""
        print("-" * 60)
    
    def input_with_prompt(self, prompt: str, required: bool = True) -> str:
        """带提示的输入"""
        while True:
            value = input(f"{prompt}: ").strip()
            if value or not required:
                return value
            print("此项为必填项，请重新输入！")
    
    def input_date(self, prompt: str, required: bool = True) -> Optional[datetime]:
        """日期输入"""
        while True:
            date_str = input(f"{prompt} (格式: YYYY-MM-DD): ").strip()
            if not date_str and not required:
                return None
            if not date_str and required:
                print("此项为必填项，请重新输入！")
                continue
            
            try:
                return datetime.strptime(date_str, "%Y-%m-%d")
            except ValueError:
                print("日期格式错误，请使用 YYYY-MM-DD 格式！")
    
    def input_int(self, prompt: str, required: bool = True) -> Optional[int]:
        """整数输入"""
        while True:
            value_str = input(f"{prompt}: ").strip()
            if not value_str and not required:
                return None
            if not value_str and required:
                print("此项为必填项，请重新输入！")
                continue
            
            try:
                return int(value_str)
            except ValueError:
                print("请输入有效的数字！")
    
    def show_main_menu(self):
        """显示主菜单"""
        self.clear_screen()
        self.print_header("手机管理系统")
        print("1. 用户管理")
        print("2. 手机管理")
        print("3. 借用管理")
        print("4. 查询追溯")
        print("5. 系统统计")
        print("0. 退出系统")
        self.print_separator()
    
    def show_user_menu(self):
        """显示用户管理菜单"""
        self.clear_screen()
        self.print_header("用户管理")
        print("1. 添加用户")
        print("2. 查看用户列表")
        print("3. 查看用户详情")
        print("4. 修改用户信息")
        print("5. 删除用户")
        print("0. 返回主菜单")
        self.print_separator()
    
    def show_phone_menu(self):
        """显示手机管理菜单"""
        self.clear_screen()
        self.print_header("手机管理")
        print("1. 添加手机")
        print("2. 查看手机列表")
        print("3. 查看手机详情")
        print("4. 修改手机信息")
        print("5. 删除手机")
        print("6. 搜索手机")
        print("0. 返回主菜单")
        self.print_separator()
    
    def show_loan_menu(self):
        """显示借用管理菜单"""
        self.clear_screen()
        self.print_header("借用管理")
        print("1. 借用手机")
        print("2. 归还手机")
        print("3. 查看活跃借用")
        print("4. 查看逾期借用")
        print("0. 返回主菜单")
        self.print_separator()
    
    def show_query_menu(self):
        """显示查询追溯菜单"""
        self.clear_screen()
        self.print_header("查询追溯")
        print("1. 手机追溯")
        print("2. 手机历史记录")
        print("3. 用户手机列表")
        print("0. 返回主菜单")
        self.print_separator()
    
    # 用户管理功能
    def add_user(self):
        """添加用户"""
        self.clear_screen()
        self.print_header("添加用户")
        
        name = self.input_with_prompt("姓名")
        department = self.input_with_prompt("部门")
        email = self.input_with_prompt("邮箱")
        phone_number = self.input_with_prompt("电话号码", required=False)
        
        success, message = self.manager.create_user(name, department, email, phone_number)
        print(f"\n{message}")
        input("\n按回车键继续...")
    
    def list_users(self):
        """显示用户列表"""
        self.clear_screen()
        self.print_header("用户列表")
        
        users = self.manager.list_users()
        if not users:
            print("暂无用户数据")
        else:
            print(f"{'ID':<5} {'姓名':<15} {'部门':<20} {'邮箱':<25}")
            self.print_separator()
            for user in users:
                print(f"{user.id:<5} {user.name:<15} {user.department:<20} {user.email:<25}")
        
        input("\n按回车键继续...")
    
    def show_user_detail(self):
        """显示用户详情"""
        self.clear_screen()
        self.print_header("用户详情")
        
        user_id = self.input_int("请输入用户ID")
        user = self.manager.get_user(user_id)
        
        if not user:
            print("用户不存在！")
        else:
            print(f"ID: {user.id}")
            print(f"姓名: {user.name}")
            print(f"部门: {user.department}")
            print(f"邮箱: {user.email}")
            print(f"电话: {user.phone_number}")
            print(f"创建时间: {user.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 显示用户的手机
            phones = self.manager.get_phones_by_user(user_id, current_only=False)
            if phones:
                print(f"\n关联手机 ({len(phones)} 台):")
                self.print_separator()
                for phone in phones:
                    status = "当前持有" if phone.current_owner_id == user_id else "原始拥有者"
                    print(f"  {phone.brand} {phone.model} (IMEI: {phone.imei}) - {status}")
        
        input("\n按回车键继续...")
    
    def update_user(self):
        """修改用户信息"""
        self.clear_screen()
        self.print_header("修改用户信息")
        
        user_id = self.input_int("请输入用户ID")
        user = self.manager.get_user(user_id)
        
        if not user:
            print("用户不存在！")
        else:
            print(f"当前用户信息: {user.name} ({user.department})")
            print("留空表示不修改该项")
            
            name = self.input_with_prompt(f"姓名 [{user.name}]", required=False)
            department = self.input_with_prompt(f"部门 [{user.department}]", required=False)
            email = self.input_with_prompt(f"邮箱 [{user.email}]", required=False)
            phone_number = self.input_with_prompt(f"电话 [{user.phone_number}]", required=False)
            
            success, message = self.manager.update_user(
                user_id, name or None, department or None, 
                email or None, phone_number
            )
            print(f"\n{message}")
        
        input("\n按回车键继续...")
    
    def delete_user(self):
        """删除用户"""
        self.clear_screen()
        self.print_header("删除用户")
        
        user_id = self.input_int("请输入用户ID")
        user = self.manager.get_user(user_id)
        
        if not user:
            print("用户不存在！")
        else:
            print(f"确认删除用户: {user.name} ({user.department})")
            confirm = input("输入 'yes' 确认删除: ").strip().lower()
            
            if confirm == 'yes':
                success, message = self.manager.delete_user(user_id)
                print(f"\n{message}")
            else:
                print("已取消删除操作")
        
        input("\n按回车键继续...")
    
    # 手机管理功能
    def add_phone(self):
        """添加手机"""
        self.clear_screen()
        self.print_header("添加手机")
        
        imei = self.input_with_prompt("IMEI编码")
        brand = self.input_with_prompt("品牌")
        model = self.input_with_prompt("型号")
        color = self.input_with_prompt("颜色")
        purchase_date = self.input_date("购买日期")
        
        # 显示用户列表供选择
        users = self.manager.list_users()
        if not users:
            print("暂无用户，请先添加用户！")
            input("\n按回车键继续...")
            return
        
        print("\n可选的初始拥有者:")
        for user in users:
            print(f"  {user.id}: {user.name} ({user.department})")
        
        original_owner_id = self.input_int("初始拥有者ID")
        notes = self.input_with_prompt("备注", required=False)
        
        success, message = self.manager.add_phone(
            imei, brand, model, color, purchase_date, original_owner_id, notes
        )
        print(f"\n{message}")
        input("\n按回车键继续...")
    
    def list_phones(self):
        """显示手机列表"""
        self.clear_screen()
        self.print_header("手机列表")
        
        phones = self.manager.list_phones()
        if not phones:
            print("暂无手机数据")
        else:
            print(f"{'IMEI':<15} {'品牌':<10} {'型号':<15} {'颜色':<10} {'状态':<12} {'当前持有者':<15}")
            self.print_separator()
            for phone in phones:
                current_owner = self.manager.get_user(phone.current_owner_id) if phone.current_owner_id else None
                owner_name = current_owner.name if current_owner else "未分配"
                print(f"{phone.imei:<15} {phone.brand:<10} {phone.model:<15} "
                      f"{phone.color:<10} {phone.status:<12} {owner_name:<15}")
        
        input("\n按回车键继续...")
    
    def show_phone_detail(self):
        """显示手机详情"""
        self.clear_screen()
        self.print_header("手机详情")
        
        imei = self.input_with_prompt("请输入IMEI编码")
        phone = self.manager.get_phone(imei)
        
        if not phone:
            print("手机不存在！")
        else:
            original_owner = self.manager.get_user(phone.original_owner_id)
            current_owner = self.manager.get_user(phone.current_owner_id) if phone.current_owner_id else None
            
            print(f"IMEI: {phone.imei}")
            print(f"品牌: {phone.brand}")
            print(f"型号: {phone.model}")
            print(f"颜色: {phone.color}")
            print(f"购买日期: {phone.purchase_date.strftime('%Y-%m-%d')}")
            print(f"状态: {phone.status}")
            print(f"原始拥有者: {original_owner.name if original_owner else '未知'}")
            print(f"当前持有者: {current_owner.name if current_owner else '未分配'}")
            print(f"备注: {phone.notes}")
            print(f"创建时间: {phone.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 显示活跃借用记录
            active_loans = self.manager.get_active_loans(imei)
            if active_loans:
                print(f"\n当前借用记录:")
                for loan in active_loans:
                    borrower = self.manager.get_user(loan.borrower_id)
                    print(f"  借用者: {borrower.name}")
                    print(f"  借用日期: {loan.loan_date.strftime('%Y-%m-%d')}")
                    if loan.expected_return_date:
                        print(f"  预期归还: {loan.expected_return_date.strftime('%Y-%m-%d')}")
        
        input("\n按回车键继续...")
    
    def update_phone(self):
        """修改手机信息"""
        self.clear_screen()
        self.print_header("修改手机信息")
        
        imei = self.input_with_prompt("请输入IMEI编码")
        phone = self.manager.get_phone(imei)
        
        if not phone:
            print("手机不存在！")
        else:
            print(f"当前手机信息: {phone.brand} {phone.model}")
            print("留空表示不修改该项")
            
            brand = self.input_with_prompt(f"品牌 [{phone.brand}]", required=False)
            model = self.input_with_prompt(f"型号 [{phone.model}]", required=False)
            color = self.input_with_prompt(f"颜色 [{phone.color}]", required=False)
            
            print(f"当前购买日期: {phone.purchase_date.strftime('%Y-%m-%d')}")
            purchase_date = self.input_date("购买日期", required=False)
            
            print("状态选项: available, in_use, maintenance, retired")
            status = self.input_with_prompt(f"状态 [{phone.status}]", required=False)
            notes = self.input_with_prompt(f"备注 [{phone.notes}]", required=False)
            
            success, message = self.manager.update_phone(
                imei, brand or None, model or None, color or None,
                purchase_date, status or None, notes
            )
            print(f"\n{message}")
        
        input("\n按回车键继续...")
    
    def delete_phone(self):
        """删除手机"""
        self.clear_screen()
        self.print_header("删除手机")
        
        imei = self.input_with_prompt("请输入IMEI编码")
        phone = self.manager.get_phone(imei)
        
        if not phone:
            print("手机不存在！")
        else:
            print(f"确认删除手机: {phone.brand} {phone.model} (IMEI: {phone.imei})")
            confirm = input("输入 'yes' 确认删除: ").strip().lower()
            
            if confirm == 'yes':
                success, message = self.manager.delete_phone(imei)
                print(f"\n{message}")
            else:
                print("已取消删除操作")
        
        input("\n按回车键继续...")
    
    def search_phones(self):
        """搜索手机"""
        self.clear_screen()
        self.print_header("搜索手机")
        
        keyword = self.input_with_prompt("关键词 (品牌/型号/IMEI/颜色)", required=False)
        status = self.input_with_prompt("状态过滤 (available/in_use/maintenance/retired)", required=False)
        user_id = self.input_int("用户ID过滤", required=False)
        
        phones = self.manager.search_phones(keyword, status, user_id)
        
        if not phones:
            print("未找到匹配的手机")
        else:
            print(f"\n找到 {len(phones)} 台手机:")
            self.print_separator()
            print(f"{'IMEI':<15} {'品牌':<10} {'型号':<15} {'颜色':<10} {'状态':<12} {'当前持有者':<15}")
            self.print_separator()
            for phone in phones:
                current_owner = self.manager.get_user(phone.current_owner_id) if phone.current_owner_id else None
                owner_name = current_owner.name if current_owner else "未分配"
                print(f"{phone.imei:<15} {phone.brand:<10} {phone.model:<15} "
                      f"{phone.color:<10} {phone.status:<12} {owner_name:<15}")
        
        input("\n按回车键继续...")
    
    # 借用管理功能
    def loan_phone(self):
        """借用手机"""
        self.clear_screen()
        self.print_header("借用手机")
        
        imei = self.input_with_prompt("请输入IMEI编码")
        phone = self.manager.get_phone(imei)
        
        if not phone:
            print("手机不存在！")
            input("\n按回车键继续...")
            return
        
        if phone.status != "available":
            print(f"手机状态为 {phone.status}，无法借出！")
            input("\n按回车键继续...")
            return
        
        current_owner = self.manager.get_user(phone.current_owner_id)
        print(f"手机信息: {phone.brand} {phone.model}")
        print(f"当前持有者: {current_owner.name if current_owner else '未分配'}")
        
        # 显示用户列表供选择
        users = self.manager.list_users()
        print("\n可选的借用者:")
        for user in users:
            if user.id != phone.current_owner_id:  # 排除当前持有者
                print(f"  {user.id}: {user.name} ({user.department})")
        
        borrower_id = self.input_int("借用者ID")
        expected_return_date = self.input_date("预期归还日期", required=False)
        purpose = self.input_with_prompt("借用目的", required=False)
        notes = self.input_with_prompt("备注", required=False)
        
        success, message = self.manager.loan_phone(
            imei, borrower_id, phone.current_owner_id, expected_return_date, purpose, notes
        )
        print(f"\n{message}")
        input("\n按回车键继续...")
    
    def return_phone(self):
        """归还手机"""
        self.clear_screen()
        self.print_header("归还手机")
        
        # 显示活跃的借用记录
        active_loans = self.manager.get_active_loans()
        if not active_loans:
            print("暂无活跃的借用记录")
            input("\n按回车键继续...")
            return
        
        print("当前活跃的借用记录:")
        self.print_separator()
        print(f"{'记录ID':<8} {'IMEI':<15} {'借用者':<15} {'借用日期':<12} {'预期归还':<12}")
        self.print_separator()
        
        for loan in active_loans:
            borrower = self.manager.get_user(loan.borrower_id)
            expected_date = loan.expected_return_date.strftime('%Y-%m-%d') if loan.expected_return_date else "未设定"
            print(f"{loan.id:<8} {loan.phone_imei:<15} {borrower.name:<15} "
                  f"{loan.loan_date.strftime('%Y-%m-%d'):<12} {expected_date:<12}")
        
        record_id = self.input_int("请输入要归还的记录ID")
        
        success, message = self.manager.return_phone(record_id)
        print(f"\n{message}")
        input("\n按回车键继续...")
    
    def show_active_loans(self):
        """显示活跃借用记录"""
        self.clear_screen()
        self.print_header("活跃借用记录")
        
        active_loans = self.manager.get_active_loans()
        if not active_loans:
            print("暂无活跃的借用记录")
        else:
            print(f"{'记录ID':<8} {'IMEI':<15} {'手机型号':<20} {'借用者':<15} {'出借者':<15} {'借用日期':<12}")
            self.print_separator()
            
            for loan in active_loans:
                phone = self.manager.get_phone(loan.phone_imei)
                borrower = self.manager.get_user(loan.borrower_id)
                lender = self.manager.get_user(loan.lender_id)
                phone_model = f"{phone.brand} {phone.model}" if phone else "未知"
                
                print(f"{loan.id:<8} {loan.phone_imei:<15} {phone_model:<20} "
                      f"{borrower.name:<15} {lender.name:<15} {loan.loan_date.strftime('%Y-%m-%d'):<12}")
        
        input("\n按回车键继续...")
    
    def show_overdue_loans(self):
        """显示逾期借用记录"""
        self.clear_screen()
        self.print_header("逾期借用记录")
        
        overdue_loans = self.manager.get_overdue_loans()
        if not overdue_loans:
            print("暂无逾期的借用记录")
        else:
            print(f"{'记录ID':<8} {'IMEI':<15} {'借用者':<15} {'预期归还':<12} {'逾期天数':<10}")
            self.print_separator()
            
            for loan in overdue_loans:
                borrower = self.manager.get_user(loan.borrower_id)
                overdue_days = (datetime.now() - loan.expected_return_date).days
                
                print(f"{loan.id:<8} {loan.phone_imei:<15} {borrower.name:<15} "
                      f"{loan.expected_return_date.strftime('%Y-%m-%d'):<12} {overdue_days:<10}")
        
        input("\n按回车键继续...")
    
    # 查询追溯功能
    def phone_trace(self):
        """手机追溯"""
        self.clear_screen()
        self.print_header("手机追溯")
        
        imei = self.input_with_prompt("请输入IMEI编码")
        success, trace_info = self.manager.get_phone_trace(imei)
        
        if success:
            print(trace_info)
        else:
            print(trace_info)
        
        input("\n按回车键继续...")
    
    def phone_history(self):
        """手机历史记录"""
        self.clear_screen()
        self.print_header("手机历史记录")
        
        imei = self.input_with_prompt("请输入IMEI编码")
        history = self.manager.get_phone_history(imei)
        
        if not history:
            print("该手机暂无历史记录")
        else:
            print(f"{'时间':<20} {'操作':<15} {'描述':<40}")
            self.print_separator()
            for record in history:
                print(f"{record.timestamp.strftime('%Y-%m-%d %H:%M:%S'):<20} "
                      f"{record.action:<15} {record.description:<40}")
        
        input("\n按回车键继续...")
    
    def user_phones(self):
        """用户手机列表"""
        self.clear_screen()
        self.print_header("用户手机列表")
        
        user_id = self.input_int("请输入用户ID")
        user = self.manager.get_user(user_id)
        
        if not user:
            print("用户不存在！")
        else:
            print(f"用户: {user.name} ({user.department})")
            
            current_phones = self.manager.get_phones_by_user(user_id, current_only=True)
            all_phones = self.manager.get_phones_by_user(user_id, current_only=False)
            
            print(f"\n当前持有手机 ({len(current_phones)} 台):")
            if current_phones:
                self.print_separator()
                for phone in current_phones:
                    print(f"  {phone.brand} {phone.model} (IMEI: {phone.imei}) - {phone.status}")
            else:
                print("  无")
            
            original_phones = [p for p in all_phones if p.original_owner_id == user_id and p.current_owner_id != user_id]
            if original_phones:
                print(f"\n原始拥有但当前不持有的手机 ({len(original_phones)} 台):")
                self.print_separator()
                for phone in original_phones:
                    current_owner = self.manager.get_user(phone.current_owner_id)
                    current_owner_name = current_owner.name if current_owner else "未分配"
                    print(f"  {phone.brand} {phone.model} (IMEI: {phone.imei}) - 当前持有者: {current_owner_name}")
        
        input("\n按回车键继续...")
    
    def show_statistics(self):
        """显示系统统计"""
        self.clear_screen()
        self.print_header("系统统计")
        
        stats = self.manager.get_statistics()
        
        print(f"总手机数: {stats['总手机数']}")
        print(f"总用户数: {stats['总用户数']}")
        print(f"活跃借用记录: {stats['活跃借用记录']}")
        print(f"逾期借用记录: {stats['逾期借用记录']}")
        
        print("\n手机状态分布:")
        self.print_separator()
        for status, count in stats['手机状态分布'].items():
            print(f"  {status}: {count} 台")
        
        input("\n按回车键继续...")
    
    def run(self):
        """运行主程序"""
        while self.running:
            try:
                self.show_main_menu()
                choice = input("请选择操作: ").strip()
                
                if choice == "1":
                    self.user_management()
                elif choice == "2":
                    self.phone_management()
                elif choice == "3":
                    self.loan_management()
                elif choice == "4":
                    self.query_management()
                elif choice == "5":
                    self.show_statistics()
                elif choice == "0":
                    print("感谢使用手机管理系统！")
                    self.running = False
                else:
                    print("无效选择，请重新输入！")
                    input("按回车键继续...")
            
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                self.running = False
            except Exception as e:
                print(f"\n发生错误: {str(e)}")
                input("按回车键继续...")
    
    def user_management(self):
        """用户管理主循环"""
        while True:
            self.show_user_menu()
            choice = input("请选择操作: ").strip()
            
            if choice == "1":
                self.add_user()
            elif choice == "2":
                self.list_users()
            elif choice == "3":
                self.show_user_detail()
            elif choice == "4":
                self.update_user()
            elif choice == "5":
                self.delete_user()
            elif choice == "0":
                break
            else:
                print("无效选择，请重新输入！")
                input("按回车键继续...")
    
    def phone_management(self):
        """手机管理主循环"""
        while True:
            self.show_phone_menu()
            choice = input("请选择操作: ").strip()
            
            if choice == "1":
                self.add_phone()
            elif choice == "2":
                self.list_phones()
            elif choice == "3":
                self.show_phone_detail()
            elif choice == "4":
                self.update_phone()
            elif choice == "5":
                self.delete_phone()
            elif choice == "6":
                self.search_phones()
            elif choice == "0":
                break
            else:
                print("无效选择，请重新输入！")
                input("按回车键继续...")
    
    def loan_management(self):
        """借用管理主循环"""
        while True:
            self.show_loan_menu()
            choice = input("请选择操作: ").strip()
            
            if choice == "1":
                self.loan_phone()
            elif choice == "2":
                self.return_phone()
            elif choice == "3":
                self.show_active_loans()
            elif choice == "4":
                self.show_overdue_loans()
            elif choice == "0":
                break
            else:
                print("无效选择，请重新输入！")
                input("按回车键继续...")
    
    def query_management(self):
        """查询追溯主循环"""
        while True:
            self.show_query_menu()
            choice = input("请选择操作: ").strip()
            
            if choice == "1":
                self.phone_trace()
            elif choice == "2":
                self.phone_history()
            elif choice == "3":
                self.user_phones()
            elif choice == "0":
                break
            else:
                print("无效选择，请重新输入！")
                input("按回车键继续...")
