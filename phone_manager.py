"""
手机管理系统 - 主业务逻辑类
提供手机管理的核心功能
"""

from datetime import datetime
from typing import List, Optional, Tuple
from models import User, Phone, PhoneLoanRecord
from database import DatabaseManager


class PhoneManager:
    """手机管理系统主类"""
    
    def __init__(self, db_path: str = "phone_manager.db"):
        self.db = DatabaseManager(db_path)
    
    # 用户管理
    def create_user(self, name: str, department: str, email: str, phone_number: str = "", password: str = None, role: str = 'user') -> Tuple[bool, str]:
        """创建用户"""
        if not password:
            return False, "密码是必填项"
        try:
            user = User(
                id=0,  # 数据库会自动分配ID
                name=name,
                department=department,
                email=email,
                phone_number=phone_number,
                role=role
            )
            user.set_password(password)
            user_id = self.db.add_user(user)
            return True, f"用户创建成功，ID: {user_id}"
        except Exception as e:
            return False, f"创建用户失败: {str(e)}"
    
    def get_user(self, user_id: int) -> Optional[User]:
        """获取用户信息"""
        return self.db.get_user(user_id)
    
    def list_users(self) -> List[User]:
        """获取所有用户列表"""
        return self.db.get_all_users()
    
    def search_users(self, keyword: str = "", sort_by: str = "id") -> List[User]:
        """搜索用户
        
        Args:
            keyword: 搜索关键词，可以搜索姓名、部门、邮箱
            sort_by: 排序方式，默认按id排序
        
        Returns:
            符合条件的用户列表
        """
        users = self.db.get_all_users()
        
        # 关键词搜索
        if keyword:
            keyword = keyword.lower().strip()
            filtered_users = []
            for user in users:
                # 在姓名、部门、邮箱中搜索
                if (keyword in user.name.lower() if user.name else False) or \
                   (keyword in user.department.lower() if user.department else False) or \
                   (keyword in user.email.lower() if user.email else False) or \
                   (keyword in user.phone_number if user.phone_number else False):
                    filtered_users.append(user)
            users = filtered_users
        
        # 排序
        if sort_by == "id":
            users.sort(key=lambda x: x.id)
        elif sort_by == "name":
            users.sort(key=lambda x: x.name.lower() if x.name else '')
        elif sort_by == "department":
            users.sort(key=lambda x: x.department.lower() if x.department else '')
        elif sort_by == "created_at":
            users.sort(key=lambda x: x.created_at if x.created_at else datetime.min)
        
        return users
    
    def update_user(self, user_id: int, name: str = None, department: str = None, 
                   email: str = None, phone_number: str = None, role: str = None) -> Tuple[bool, str]:
        """更新用户信息"""
        user = self.db.get_user(user_id)
        if not user:
            return False, "用户不存在"
        
        if name:
            user.name = name
        if department:
            user.department = department
        if email:
            user.email = email
        if phone_number is not None:
            user.phone_number = phone_number
        if role and role in ['user', 'admin']:
            user.role = role
        
        success = self.db.update_user(user)
        return success, "用户信息更新成功" if success else "更新失败"
    
    def delete_user(self, user_id: int) -> Tuple[bool, str]:
        """删除用户"""
        phones = self.db.get_phones_by_user(user_id, current_only=False)
        if phones:
            return False, f"无法删除用户，该用户有 {len(phones)} 台关联手机"
        
        success = self.db.delete_user(user_id)
        return success, "用户删除成功" if success else "删除失败"
    
    # 手机管理
    def add_phone(self, imei: str, brand: str, model: str, color: str, project: str,
                 original_owner_id: int, notes: str = "") -> Tuple[bool, str]:
        """添加手机"""
        if self.db.get_phone(imei):
            return False, "IMEI已存在"
        if not self.db.get_user(original_owner_id):
            return False, "原始拥有者不存在"
        
        try:
            phone = Phone(
                imei=imei, brand=brand, model=model, color=color, project=project,
                original_owner_id=original_owner_id,
                current_owner_id=original_owner_id,
                status="available", notes=notes
            )
            success = self.db.add_phone(phone)
            return success, "手机添加成功" if success else "添加失败"
        except Exception as e:
            return False, f"添加手机失败: {str(e)}"

    # ... (other methods remain the same for now)
    def batch_add_phones(self, phones_data: List[dict]) -> Tuple[bool, str, List[str]]:
        """批量添加手机"""
        success_list = []
        error_list = []
        
        for phone_data in phones_data:
            try:
                success, msg = self.add_phone(
                    phone_data['imei'],
                    phone_data['brand'],
                    phone_data['model'],
                    phone_data['color'],
                    phone_data['project'],
                    phone_data['original_owner_id'],
                    phone_data.get('notes', '')
                )
                if success:
                    success_list.append(f"✓ {phone_data['imei']}: {msg}")
                else:
                    error_list.append(f"✗ {phone_data['imei']}: {msg}")
            except Exception as e:
                error_list.append(f"✗ {phone_data.get('imei', '未知')}: {str(e)}")
        
        total = len(phones_data)
        success_count = len(success_list)
        error_count = len(error_list)
        
        if success_count == total:
            return True, f"批量添加成功：{success_count}/{total} 台手机", success_list
        elif success_count > 0:
            return True, f"部分成功：{success_count}/{total} 台手机添加成功，{error_count} 台失败", success_list + error_list
        else:
            return False, f"批量添加失败：0/{total} 台手机添加成功", error_list
    
    def get_phone(self, imei: str) -> Optional[Phone]:
        return self.db.get_phone(imei)
    
    def list_phones(self) -> List[Phone]:
        return self.db.get_all_phones()
    
    def update_phone(self, imei: str, brand: str = None, model: str = None, 
                    color: str = None, project: str = None, status: str = None, notes: str = None) -> Tuple[bool, str]:
        phone = self.db.get_phone(imei)
        if not phone:
            return False, "手机不存在"
        
        if brand: phone.brand = brand
        if model: phone.model = model
        if color: phone.color = color
        if project: phone.project = project
        if status: phone.status = status
        if notes is not None: phone.notes = notes
        
        success = self.db.update_phone(phone)
        return success, "手机信息更新成功" if success else "更新失败"
    
    def delete_phone(self, imei: str) -> Tuple[bool, str]:
        if self.db.get_active_loan_records(imei):
            return False, "无法删除手机，存在活跃的借用记录"
        
        success = self.db.delete_phone(imei)
        return success, "手机删除成功" if success else "删除失败"
    
    def get_phones_by_user(self, user_id: int, current_only: bool = True) -> List[Phone]:
        return self.db.get_phones_by_user(user_id, current_only)
    
    def search_phones(self, keyword: str = "", status: str = "", user_id: int = None, project: str = "") -> List[Phone]:
        phones = self.db.get_all_phones()
        if keyword:
            keyword = keyword.lower()
            phones = [p for p in phones if keyword in p.imei.lower() or 
                      keyword in p.brand.lower() or 
                      keyword in p.model.lower() or 
                      keyword in p.color.lower()]
        if project:
            phones = [p for p in phones if p.project and project.lower() in p.project.lower()]
        if status:
            phones = [p for p in phones if p.status == status]
        if user_id:
            phones = [p for p in phones if p.current_owner_id == user_id]
        return phones
    
    # 手机借用管理
    def loan_phone(self, phone_imei: str, borrower_id: int, lender_id: int, expected_return_date: Optional[datetime] = None,
                  purpose: str = "", notes: str = "") -> Tuple[bool, str]:
        phone = self.db.get_phone(phone_imei)
        if not phone: 
            return False, "手机不存在"
        
        # 检查用户是否存在
        if not self.db.get_user(borrower_id): 
            return False, "借用者不存在"
        if not self.db.get_user(lender_id): 
            return False, "出借者不存在"
        if lender_id == borrower_id: 
            return False, "不能向自己借用手机"
        
        # 检查出借者是否是当前持有者
        if phone.current_owner_id != lender_id:
            current_owner = self.db.get_user(phone.current_owner_id) if phone.current_owner_id else None
            return False, f"出借者不是当前持有者。当前持有者是: {current_owner.name if current_owner else '未分配'}"
        
        # 检查手机是否可借出（只有状态为 available 或 in_use 且没有活跃借用记录的手机才能借出）
        if phone.status not in ["available", "in_use"]:
            return False, f"手机状态为 {phone.status}，无法借出"
        
        # 检查是否已经有活跃的借用记录（即手机已经被借出）
        active_loans = self.db.get_active_loan_records(phone_imei)
        if active_loans:
            # 检查最后一个借用记录的借用者是否是当前的出借者
            last_loan = active_loans[0]  # get_active_loan_records 按时间倒序排列
            if last_loan.borrower_id != lender_id:
                last_borrower = self.db.get_user(last_loan.borrower_id)
                return False, f"手机已被借出，当前在 {last_borrower.name if last_borrower else '未知用户'} 手中，无法再次借出"
        
        try:
            loan_record = PhoneLoanRecord(
                id=0, phone_imei=phone_imei, borrower_id=borrower_id, lender_id=lender_id,
                loan_date=datetime.now(), expected_return_date=expected_return_date,
                purpose=purpose, status="active", notes=notes
            )
            record_id = self.db.add_loan_record(loan_record)
            return True, f"手机借用成功，记录ID: {record_id}"
        except Exception as e:
            return False, f"借用手机失败: {str(e)}"
    
    def return_phone(self, record_id: int, returner_id: int) -> Tuple[bool, str]:
        loan_record = self.db.get_loan_record(record_id)
        if not loan_record: 
            return False, "借用记录不存在"

        returner = self.db.get_user(returner_id)
        if not returner: 
            return False, "操作用户不存在"

        # 检查是否有权限归还（借用者或管理员）
        if loan_record.borrower_id != returner_id and returner.role != 'admin':
            return False, "只有借用者或管理员才能归还手机"
        
        # 获取手机的借用链（按时间顺序）
        loan_chain = self.db.get_loan_chain(loan_record.phone_imei)
        
        # 检查是否是最后一个借用记录（即当前持有者）
        if len(loan_chain) > 1:
            # 找到当前记录在链中的位置
            current_index = -1
            for i, loan in enumerate(loan_chain):
                if loan.id == record_id:
                    current_index = i
                    break
            
            if current_index == -1:
                return False, "无法找到指定的借用记录"
            
            # 检查是否在之后还有其他借用记录
            if current_index < len(loan_chain) - 1:
                next_loan = loan_chain[current_index + 1]
                next_borrower = self.db.get_user(next_loan.borrower_id)
                return False, f"不能直接归还，手机当前在 {next_borrower.name if next_borrower else '未知用户'} 手中。请先让 {next_borrower.name if next_borrower else '未知用户'} 归还手机。"

        try:
            success = self.db.return_phone(record_id)
            return success, "手机归还成功" if success else "归还失败或记录不存在"
        except Exception as e:
            return False, f"归还手机失败: {str(e)}"

    def get_active_loans(self, phone_imei: str = None) -> List[PhoneLoanRecord]:
        return self.db.get_active_loan_records(phone_imei)

    def get_loan_chain_info(self, phone_imei: str) -> Tuple[bool, str, List[dict]]:
        """获取手机的借用链信息"""
        try:
            loan_chain = self.db.get_loan_chain(phone_imei)
            if not loan_chain:
                return True, "没有活跃的借用记录", []
            
            chain_info = []
            for i, loan in enumerate(loan_chain):
                borrower = self.db.get_user(loan.borrower_id)
                lender = self.db.get_user(loan.lender_id)
                
                chain_info.append({
                    'id': loan.id,
                    'step': i + 1,
                    'borrower_name': borrower.name if borrower else '未知用户',
                    'lender_name': lender.name if lender else '未知用户',
                    'loan_date': loan.loan_date,
                    'purpose': loan.purpose,
                    'is_current_holder': i == len(loan_chain) - 1
                })
            
            return True, f"借用链长度: {len(loan_chain)}", chain_info
        except Exception as e:
            return False, f"获取借用链信息失败: {str(e)}", []

    def get_overdue_loans(self) -> List[PhoneLoanRecord]:
        active_loans = self.db.get_active_loan_records()
        return [loan for loan in active_loans if loan.expected_return_date and loan.expected_return_date < datetime.now()]

    def get_phone_history(self, phone_imei: str):
        return self.db.get_phone_history(phone_imei)

    def get_phone_trace(self, phone_imei: str) -> Tuple[bool, str]:
        """获取手机追溯信息"""
        phone = self.db.get_phone(phone_imei)
        if not phone:
            return False, "手机不存在"
        
        try:
            # 获取基本信息
            original_owner = self.db.get_user(phone.original_owner_id)
            current_owner = self.db.get_user(phone.current_owner_id) if phone.current_owner_id else None
            
            # 获取历史记录
            history = self.db.get_phone_history(phone_imei)
            
            # 获取借用记录
            active_loans = self.db.get_active_loan_records(phone_imei)
            
            # 构建追溯信息
            trace_info = []
            trace_info.append(f"手机追溯信息 - IMEI: {phone_imei}")
            trace_info.append("=" * 60)
            trace_info.append(f"手机信息: {phone.brand} {phone.model} ({phone.color})")
            trace_info.append(f"当前状态: {phone.status}")
            trace_info.append(f"原始拥有者: {original_owner.name if original_owner else '未知'} ({original_owner.department if original_owner else '未知'})")
            trace_info.append(f"当前持有者: {current_owner.name if current_owner else '未分配'} ({current_owner.department if current_owner else ''})")
            
            if active_loans:
                trace_info.append("\n当前借用状态:")
                for loan in active_loans:
                    borrower = self.db.get_user(loan.borrower_id)
                    lender = self.db.get_user(loan.lender_id)
                    trace_info.append(f"  借用者: {borrower.name if borrower else '未知'}")
                    trace_info.append(f"  出借者: {lender.name if lender else '未知'}")
                    trace_info.append(f"  借用日期: {loan.loan_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    if loan.expected_return_date:
                        trace_info.append(f"  预期归还: {loan.expected_return_date.strftime('%Y-%m-%d')}")
            
            if history:
                trace_info.append("\n历史记录:")
                trace_info.append("-" * 60)
                for record in history:
                    from_user = self.db.get_user(record.from_user_id) if record.from_user_id else None
                    to_user = self.db.get_user(record.to_user_id) if record.to_user_id else None
                    
                    time_str = record.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    action_str = record.action
                    desc_str = record.description
                    
                    if from_user and to_user:
                        trace_info.append(f"  {time_str} - {action_str}: {from_user.name} -> {to_user.name} ({desc_str})")
                    elif to_user:
                        trace_info.append(f"  {time_str} - {action_str}: -> {to_user.name} ({desc_str})")
                    else:
                        trace_info.append(f"  {time_str} - {action_str}: {desc_str}")
            
            return True, "\n".join(trace_info)
            
        except Exception as e:
            return False, f"获取追溯信息失败: {str(e)}"

    def get_statistics(self) -> dict:
        phones = self.db.get_all_phones()
        users = self.db.get_all_users()
        active_loans = self.db.get_active_loan_records()
        status_count = {}
        for phone in phones:
            status_count[phone.status] = status_count.get(phone.status, 0) + 1
        
        return {
            "总手机数": len(phones),
            "总用户数": len(users),
            "活跃借用记录": len(active_loans),
            "逾期借用记录": len(self.get_overdue_loans()),
            "手机状态分布": status_count
        }