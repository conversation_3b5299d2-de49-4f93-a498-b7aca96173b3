# 手机管理系统

一个基于Python Flask的现代化手机管理系统，支持手机的增删改查、借用管理和完整的追溯功能。

## 功能特性

### 核心功能
- **手机管理**: 完整的手机信息管理（增删改查）
- **用户管理**: 用户信息维护和管理
- **借用管理**: 手机借用、归还流程管理
- **追溯功能**: 完整的手机历史轨迹追踪
- **IMEI唯一性**: 基于IMEI编码的唯一标识

### 界面支持
- **Web界面**: 现代化的响应式Web界面
- **命令行界面**: 传统的CLI界面支持
- **双模式运行**: 可根据需要选择运行模式

### 特色功能
- 📱 手机状态管理（可用、使用中、维护中、已退役）
- 👥 用户部门管理
- 📋 借用记录跟踪
- ⏰ 逾期提醒功能
- 📊 系统统计面板
- 🔍 强大的搜索筛选功能
- 📈 完整的历史记录

## 系统架构

```
PhoneManager/
├── models.py          # 数据模型定义
├── database.py        # 数据库管理
├── phone_manager.py   # 核心业务逻辑
├── app.py            # Flask Web应用
├── cli.py            # 命令行界面
├── main.py           # 主程序入口
├── requirements.txt   # 依赖包列表
├── templates/        # HTML模板文件
│   ├── base.html
│   ├── index.html
│   ├── users.html
│   ├── phones.html
│   └── ...
└── static/          # 静态资源文件
    ├── css/
    ├── js/
    └── ...
```

## 快速开始

### 1. 环境要求
- Python 3.7+
- pip

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行系统

#### Web界面模式（推荐）
```bash
python main.py --mode web
```
然后在浏览器中访问 `http://localhost:5000`

#### 命令行界面模式
```bash
python main.py --mode cli
```

#### 自定义配置
```bash
# 指定端口和主机
python main.py --mode web --host 0.0.0.0 --port 8080

# 启用调试模式
python main.py --mode web --debug
```

## 使用说明

### Web界面使用

1. **首页概览**: 显示系统统计信息和快捷操作
2. **用户管理**: 
   - 添加、编辑、删除用户
   - 查看用户详情和关联手机
3. **手机管理**:
   - 添加新手机（需要IMEI、品牌、型号等信息）
   - 搜索和筛选手机
   - 查看手机详情和历史记录
4. **借用管理**:
   - 创建借用记录
   - 处理手机归还
   - 查看逾期提醒

### 数据模型

#### 用户（User）
- ID、姓名、部门、邮箱、电话
- 创建时间

#### 手机（Phone）
- IMEI编码（唯一标识）
- 品牌、型号、颜色
- 购买日期、状态
- 原始拥有者、当前持有者
- 备注信息

#### 借用记录（PhoneLoanRecord）
- 借用者、出借者
- 借用日期、预期归还日期、实际归还日期
- 借用目的、状态、备注

#### 历史记录（PhoneHistory）
- 操作类型、时间戳
- 操作描述、相关用户

## 系统特性

### 手机状态管理
- **available**: 可用状态，可以被借用
- **in_use**: 使用中，已被借用
- **maintenance**: 维护中，暂不可用
- **retired**: 已退役，不再使用

### 借用流程
1. 选择可用状态的手机
2. 指定借用者和预期归还日期
3. 系统自动更新手机状态和持有者
4. 生成借用记录和历史轨迹
5. 归还时恢复手机状态

### 追溯功能
- 完整的手机生命周期记录
- 所有权变更历史
- 借用归还轨迹
- 维护记录追踪

### 安全特性
- IMEI唯一性校验
- 数据完整性检查
- 级联删除保护
- 操作权限控制

## 开发说明

### 技术栈
- **后端**: Python Flask
- **数据库**: SQLite
- **前端**: Bootstrap 5 + JavaScript
- **图标**: Bootstrap Icons

### 代码结构
- **models.py**: 使用dataclass定义数据模型
- **database.py**: SQLite数据库操作封装
- **phone_manager.py**: 业务逻辑层
- **app.py**: Flask路由和API
- **cli.py**: 命令行界面实现

### 扩展建议
1. **数据库升级**: 可替换为MySQL/PostgreSQL
2. **用户认证**: 添加登录和权限系统
3. **API接口**: 提供RESTful API
4. **移动端**: 开发移动应用
5. **报表功能**: 添加数据分析和报表
6. **消息通知**: 集成邮件/短信通知
7. **批量操作**: 支持批量导入导出

## 故障排除

### 常见问题

1. **数据库文件权限错误**
   ```bash
   chmod 664 phone_manager.db
   ```

2. **端口被占用**
   ```bash
   python main.py --mode web --port 8080
   ```

3. **依赖包安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

### 数据备份
```bash
# 备份数据库文件
cp phone_manager.db phone_manager_backup_$(date +%Y%m%d).db
```

### 重置数据
删除 `phone_manager.db` 文件，系统会在下次启动时自动创建新的数据库。

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 基础的手机管理功能
- Web界面和CLI界面
- 用户管理和借用管理
- 完整的追溯功能
- 响应式设计
