/* 手机管理系统 - 现代简约UI样式 */

:root {
    /* 主色调 - 现代蓝色系 */
    --primary: #2563eb;
    --primary-dark: #1e40af;
    --primary-light: #3b82f6;
    --primary-subtle: #eff6ff;
    
    /* 中性色 - 简约灰色系 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 功能色 */
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #06b6d4;
    
    /* 阴影 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    
    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
}

/* 重置和基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 导航栏 */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--gray-900) !important;
    letter-spacing: -0.025em;
}

.navbar-nav .nav-link {
    color: var(--gray-600) !important;
    font-weight: 500;
    transition: color 0.2s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary) !important;
}

/* 卡片设计 */
.card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xs);
    transition: all 0.2s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.card-header {
    background: white;
    border-bottom: 1px solid var(--gray-100);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

/* 统计卡片 */
.stat-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: all 0.2s ease;
    text-decoration: none;
    display: block;
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
    border-color: var(--gray-300);
}

.stat-card .stat-icon {
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
    letter-spacing: -0.025em;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-600);
    margin: 0;
    margin-top: 0.25rem;
}

/* 按钮设计 */
.btn {
    font-weight: 500;
    border-radius: var(--radius);
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
    background: white;
}

.btn-outline-primary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
    border-radius: var(--radius-sm);
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
}

/* 表格设计 */
.table {
    margin: 0;
}

.table thead th {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.8125rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    padding: 0.75rem 1rem;
}

.table tbody td {
    padding: 0.875rem 1rem;
    border-bottom: 1px solid var(--gray-100);
    vertical-align: middle;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

/* 徽章设计 */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.25rem 0.625rem;
    border-radius: 9999px;
    letter-spacing: 0.025em;
}

.bg-success {
    background: var(--success) !important;
}

.bg-primary {
    background: var(--primary) !important;
}

.bg-warning {
    background: var(--warning) !important;
    color: white !important;
}

.bg-secondary {
    background: var(--gray-500) !important;
}

/* 表单设计 */
.form-control, .form-select {
    border: 1px solid var(--gray-300);
    border-radius: var(--radius);
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.375rem;
    font-size: 0.875rem;
}

/* 输入组 */
.input-group-text {
    background: var(--gray-50);
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
}

/* 分页 */
.page-link {
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
    background: white;
}

.page-link:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.page-item.active .page-link {
    background: var(--primary);
    border-color: var(--primary);
}

/* 工具提示和下拉菜单 */
.dropdown-menu {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.dropdown-item:hover {
    background: var(--gray-50);
}

/* 警告框 */
.alert {
    border-radius: var(--radius-md);
    border: none;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid var(--success);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left: 4px solid var(--danger);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid var(--warning);
}

.alert-info {
    background: rgba(6, 182, 212, 0.1);
    color: #155e75;
    border-left: 4px solid var(--info);
}

/* 模态框 */
.modal-content {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid var(--gray-200);
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    padding: 1.5rem;
    gap: 0.75rem;
}

/* 进度条 */
.progress {
    background: var(--gray-100);
    border-radius: 9999px;
    overflow: hidden;
}

.progress-bar {
    border-radius: 9999px;
    transition: width 0.6s ease;
}

/* 状态指示器 */
.status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-dot.status-available {
    background: var(--success);
}

.status-dot.status-in_use {
    background: var(--primary);
}

.status-dot.status-maintenance {
    background: var(--warning);
}

.status-dot.status-retired {
    background: var(--gray-500);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.5rem;
        font-size: 0.8125rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 代码块样式 */
code {
    background: var(--gray-100);
    color: var(--gray-800);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-sm);
    font-size: 0.8125rem;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 链接样式 */
a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-dark);
}

/* 文本颜色工具类 */
.text-muted {
    color: var(--gray-600) !important;
}

.text-primary {
    color: var(--primary) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-danger {
    color: var(--danger) !important;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    background-color: #f8f9fa !important;
    border-top: 1px solid #dee2e6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group {
        display: flex;
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 2px;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .stats-card .stats-icon {
        font-size: 2rem;
    }
}

/* 自定义动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 1rem;
    height: 1rem;
    margin-left: 0.5rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-available {
    background-color: var(--success-color);
}

.status-in-use {
    background-color: var(--primary-color);
}

.status-maintenance {
    background-color: var(--warning-color);
}

.status-retired {
    background-color: var(--secondary-color, #6c757d);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #212529;
    border-radius: 6px;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h4 {
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* 搜索高亮 */
.search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .card-header,
    footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 0.875rem;
    }
}

/* =============================
   重新设计的登录注册页面样式
   ============================= */

/* 页面基础设置 */
.login-page, .register-page {
    overflow: hidden;
    height: 100vh;
    background: #ffffff;
    position: relative;
}



/* 隐藏导航栏和页脚 */
.login-page .navbar,
.login-page footer,
.register-page .navbar,
.register-page footer {
    display: none !important;
}

/* main容器重置 */
.login-page main, .register-page main {
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
    width: 100% !important;
    height: 100vh !important;
    position: relative;
    z-index: 1;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 认证容器 */
.auth-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
}

/* 认证卡片 - 统一尺寸 */
.auth-card {
    width: 100%;
    max-width: 500px;
    min-height: 600px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 20px;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    padding: 40px;
    box-sizing: border-box;
}

.auth-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 头部区域 */
.auth-header {
    text-align: center;
    margin-bottom: 40px;
}

.auth-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    margin-bottom: 20px;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.auth-icon i {
    font-size: 2.5rem;
    color: white;
}

.auth-title {
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0 0 10px 0;
}

.auth-subtitle {
    color: rgba(102, 126, 234, 0.7);
    font-weight: 500;
    font-size: 1rem;
    margin: 0;
}

/* 表单区域 */
.auth-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-group {
    margin-bottom: 25px;
}

.form-group.half-width {
    flex: 1;
    margin-right: 15px;
}

.form-group.half-width:last-child {
    margin-right: 0;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.form-label {
    display: flex;
    align-items: center;
    color: #4c5c96;
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 8px;
}

.form-label i {
    color: #667eea;
    margin-right: 8px;
    font-size: 1rem;
}

.form-input {
    width: 100%;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-input:focus {
    background: #ffffff;
    border-color: #667eea;
    box-shadow: 
        0 0 0 3px rgba(102, 126, 234, 0.1),
        0 2px 8px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
    outline: none;
}

.form-input::placeholder {
    color: rgba(76, 92, 150, 0.6);
}

/* 空间填充区域（仅登录页面使用） */
.spacer-area {
    height: 120px;
}

/* 按钮样式 */
.auth-button {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 12px;
    padding: 14px 24px;
    font-weight: 600;
    font-size: 1rem;
    color: white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auth-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.auth-button:hover::before {
    left: 100%;
}

.auth-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.auth-button:active {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* 底部区域 */
.auth-footer {
    text-align: center;
    margin-top: 30px;
}

.auth-footer p {
    color: rgba(102, 126, 234, 0.7);
    font-size: 0.95rem;
    margin: 0;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-container {
        padding: 15px;
    }
    
    .auth-card {
        min-height: 500px;
        padding: 30px 25px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-group.half-width {
        margin-right: 0;
        margin-bottom: 25px;
    }
    
    .spacer-area {
        height: 80px;
    }
}

/* =============================
   简约化设计补充样式
   ============================= */
   
/* 表格无边框优化 */
.table > :not(caption) > * > .border-0 {
    border-bottom: none !important;
}

/* 按钮组间距优化 */
.btn-group-sm > .btn + .btn {
    margin-left: -1px;
}

/* 徽章样式统一 */
.badge.bg-light {
    color: var(--gray-700) !important;
    background-color: var(--gray-100) !important;
    border: 1px solid var(--gray-200);
}

/* 搜索过滤器卡片无阴影 */
.search-filters .card {
    box-shadow: none !important;
    border: 1px solid var(--gray-200);
}

/* 输入组边框优化 */
.input-group .form-control:not(:first-child) {
    border-left: none;
}

.input-group .form-control:not(:last-child) {
    border-right: none;
}

/* 小尺寸按钮 */
.btn-xs {
    padding: 0.125rem 0.375rem;
    font-size: 0.75rem;
    line-height: 1.25;
    border-radius: calc(var(--radius) * 0.75);
}

/* 文本权重 */
.fw-medium {
    font-weight: 500 !important;
}

.fw-semibold {
    font-weight: 600 !important;
}

/* 现代化进度条 */
.progress {
    height: 8px;
    background-color: var(--gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* 空状态优化 */
.empty-state {
    padding: 3rem 2rem;
}

.empty-state i {
    opacity: 0.4;
    margin-bottom: 1rem;
}

/* 状态徽章现代化 */
.badge {
    font-weight: 500;
    letter-spacing: 0.025em;
}

.bg-success-subtle {
    background-color: rgba(34, 197, 94, 0.1) !important;
    color: #166534 !important;
}

.bg-primary-subtle {
    background-color: rgba(59, 130, 246, 0.1) !important;
    color: #1e40af !important;
}

.bg-danger-subtle {
    background-color: rgba(239, 68, 68, 0.1) !important;
    color: #991b1b !important;
}

.bg-secondary-subtle {
    background-color: rgba(107, 114, 128, 0.1) !important;
    color: #374151 !important;
}

/* 移动端响应式优化 */
@media (max-width: 768px) {
    .stat-card .stat-number {
        font-size: 1.75rem;
    }
    
    .stat-card .stat-icon {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .search-filters .card-body {
        padding: 1rem;
    }
}
