{% extends "base.html" %}

{% block title %}手机追溯 - {{ phone.brand }} {{ phone.model }} - 手机管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2 mb-0">
                <i class="bi bi-diagram-3"></i> 手机追溯
            </h1>
            <div>
                <a href="{{ url_for('phone_detail', imei=phone.imei) }}" class="btn btn-info me-2">
                    <i class="bi bi-eye"></i> 查看详情
                </a>
                <a href="{{ url_for('phones') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回手机列表
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 手机基本信息 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-phone"></i> 手机基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">IMEI编码:</th>
                                <td><code>{{ phone.imei }}</code></td>
                            </tr>
                            <tr>
                                <th>品牌型号:</th>
                                <td><strong>{{ phone.brand }} {{ phone.model }}</strong></td>
                            </tr>
                            <tr>
                                <th>颜色:</th>
                                <td>{{ phone.color }}</td>
                            </tr>
                            <tr>
                                <th>购买日期:</th>
                                <td>{{ phone.created_at|date if phone.created_at else '未设置' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="30%">当前状态:</th>
                                <td>
                                    {% if phone.status == 'available' %}
                                        <span class="badge bg-success">可用</span>
                                    {% elif phone.status == 'in_use' %}
                                        <span class="badge bg-primary">使用中</span>
                                    {% elif phone.status == 'maintenance' %}
                                        <span class="badge bg-warning">维护中</span>
                                    {% elif phone.status == 'retired' %}
                                        <span class="badge bg-secondary">已退役</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>原始拥有者:</th>
                                <td>
                                    {% if original_owner %}
                                        <strong>{{ original_owner.name }}</strong>
                                        <small class="text-muted">({{ original_owner.department }})</small>
                                    {% else %}
                                        <span class="text-muted">未知用户</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>当前持有者:</th>
                                <td>
                                    {% if current_owner %}
                                        <strong>{{ current_owner.name }}</strong>
                                        <small class="text-muted">({{ current_owner.department }})</small>
                                    {% else %}
                                        <span class="text-muted">未分配</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>入库时间:</th>
                                <td>{{ phone.created_at.strftime('%Y-%m-%d %H:%M:%S') if phone.created_at else '未设置' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                {% if phone.notes %}
                    <div class="mt-3">
                        <h6>备注:</h6>
                        <p class="text-muted">{{ phone.notes }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 当前借用状态 -->
{% if active_loans %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h5 class="alert-heading">
                <i class="bi bi-info-circle"></i> 当前借用状态
            </h5>
            {% for loan in active_loans %}
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>借用者:</strong> 
                            {% if loan.borrower %}
                                {{ loan.borrower.name }} ({{ loan.borrower.department }})
                            {% else %}
                                未知用户
                            {% endif %}
                        </p>
                        <p class="mb-1"><strong>出借者:</strong> 
                            {% if loan.lender %}
                                {{ loan.lender.name }} ({{ loan.lender.department }})
                            {% else %}
                                未知用户
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>借用日期:</strong> {{ loan.loan_date|date }}</p>
                        {% if loan.expected_return_date %}
                            <p class="mb-1"><strong>预期归还:</strong> {{ loan.expected_return_date|date }}</p>
                        {% endif %}
                    </div>
                </div>
                {% if loan.purpose %}
                    <p class="mb-1"><strong>借用目的:</strong> {{ loan.purpose }}</p>
                {% endif %}
                {% if not loop.last %}<hr>{% endif %}
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- 历史轨迹 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> 历史轨迹
                </h5>
            </div>
            <div class="card-body">
                {% if history %}
                    <div class="timeline">
                        {% for record in history %}
                            <div class="timeline-item">
                                <div class="timeline-marker">
                                    {% if record.action == 'created' %}
                                        <i class="bi bi-plus-circle text-success"></i>
                                    {% elif record.action == 'transferred' %}
                                        <i class="bi bi-arrow-left-right text-primary"></i>
                                    {% elif record.action == 'returned' %}
                                        <i class="bi bi-check-circle text-info"></i>
                                    {% elif record.action == 'maintenance' %}
                                        <i class="bi bi-tools text-warning"></i>
                                    {% elif record.action == 'retired' %}
                                        <i class="bi bi-archive text-secondary"></i>
                                    {% else %}
                                        <i class="bi bi-circle text-muted"></i>
                                    {% endif %}
                                </div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">
                                                {% if record.action == 'created' %}
                                                    手机入库
                                                {% elif record.action == 'transferred' %}
                                                    手机转移
                                                {% elif record.action == 'returned' %}
                                                    手机归还
                                                {% elif record.action == 'maintenance' %}
                                                    维护记录
                                                {% elif record.action == 'retired' %}
                                                    手机退役
                                                {% else %}
                                                    {{ record.action }}
                                                {% endif %}
                                            </h6>
                                            <p class="mb-1 text-muted">{{ record.description }}</p>
                                        </div>
                                        <small class="text-muted">{{ record.timestamp.strftime('%Y-%m-%d %H:%M:%S') if record.timestamp else '未设置' }}</small>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-clock-history display-4 text-muted"></i>
                        <h5 class="mt-3 text-muted">暂无历史记录</h5>
                        <p class="text-muted">该手机还没有任何操作记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block head %}
<style>
/* 时间线样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 0;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #0d6efd;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-item:last-child::after {
    content: '';
    position: absolute;
    left: -19px;
    bottom: -15px;
    width: 8px;
    height: 8px;
    background: #dee2e6;
    border-radius: 50%;
}
</style>
{% endblock %}
