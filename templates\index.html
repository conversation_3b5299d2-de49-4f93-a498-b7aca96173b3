{% extends "base.html" %}

{% block title %}系统概览 - 手机管理系统{% endblock %}

{% block content %}
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-5">
    <div>
        <h1 class="h2 mb-1 fw-bold">系统概览</h1>
        <p class="text-muted mb-0">手机设备管理一览表</p>
    </div>
    <div class="text-end">
        <small class="text-muted">最后更新: <span id="lastUpdate">{{ datetime.now().strftime('%Y-%m-%d %H:%M:%S') }}</span></small>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row g-4 mb-5">
    <div class="col-xl-3 col-md-6">
        <a href="{{ url_for('phones') }}" class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-primary bg-opacity-10">
                    <i class="bi bi-phone text-primary"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number text-primary" id="totalPhones">{{ stats['总手机数'] }}</div>
                    <div class="stat-label">总手机数</div>
                </div>
            </div>
        </a>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <a href="{{ url_for('users') }}" class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-success bg-opacity-10">
                    <i class="bi bi-people text-success"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number text-success" id="totalUsers">{{ stats['总用户数'] }}</div>
                    <div class="stat-label">总用户数</div>
                </div>
            </div>
        </a>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <a href="{{ url_for('loans') }}" class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-info bg-opacity-10">
                    <i class="bi bi-arrow-left-right text-info"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number text-info" id="activeLoans">{{ stats['活跃借用记录'] }}</div>
                    <div class="stat-label">活跃借用</div>
                </div>
            </div>
        </a>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <a href="{{ url_for('loans') }}{% if stats['逾期借用记录'] > 0 %}?filter=overdue{% endif %}" class="stat-card">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-warning bg-opacity-10">
                    <i class="bi bi-exclamation-triangle text-warning"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="stat-number text-warning" id="overdueLoans">{{ stats['逾期借用记录'] }}</div>
                    <div class="stat-label">逾期借用</div>
                </div>
            </div>
        </a>
    </div>
</div>

<div class="row g-4">
    <!-- 手机状态分布 -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-bar-chart me-2 text-primary"></i>手机状态分布
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshStatusBars()">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
            </div>
            <div class="card-body">
                {% if stats['手机状态分布'] %}
                    <div id="statusBars">
                        {% set total_phones = stats['总手机数'] %}
                        {% for status, count in stats['手机状态分布'].items() %}
                            {% set percentage = (count / total_phones * 100) if total_phones > 0 else 0 %}
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="status-dot status-{{ status }}"></div>
                                        <span class="fw-medium">
                                            {% if status == 'available' %}
                                                可用
                                            {% elif status == 'in_use' %}
                                                使用中
                                            {% elif status == 'maintenance' %}
                                                维护中
                                            {% elif status == 'retired' %}
                                                已退役
                                            {% else %}
                                                {{ status }}
                                            {% endif %}
                                        </span>
                                    </div>
                                    <div class="text-end">
                                        <span class="fw-bold">{{ count }}</span>
                                        <small class="text-muted ms-1">({{ "%.1f"|format(percentage) }}%)</small>
                                    </div>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-{{ 'success' if status == 'available' else 'primary' if status == 'in_use' else 'warning' if status == 'maintenance' else 'secondary' }}" 
                                         role="progressbar" 
                                         style="width: 0%" 
                                         data-width="{{ percentage }}">
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-bar-chart display-4 text-muted"></i>
                        <p class="text-muted mt-3">暂无数据</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 最近添加的手机 -->
    <div class="col-lg-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history me-2 text-primary"></i>最近添加的手机
                </h5>
                <a href="{{ url_for('phones') }}" class="btn btn-outline-primary btn-sm">
                    查看全部 <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-body">
                {% if recent_phones %}
                    <div class="d-flex flex-column gap-3">
                        {% for phone in recent_phones %}
                            <div class="d-flex align-items-center p-3 bg-light rounded">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-white rounded-circle p-2 shadow-sm">
                                        <i class="bi bi-phone text-primary"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-medium">{{ phone.brand }} {{ phone.model }}</h6>
                                    <div class="small text-muted mb-1">
                                        <i class="bi bi-tag me-1"></i>{{ phone.project }}
                                    </div>
                                    <div class="small text-muted">
                                        <i class="bi bi-hash me-1"></i>{{ phone.imei[:8] }}...
                                    </div>
                                </div>
                                <div class="text-end">
                                    {% if phone.status == 'available' %}
                                        <span class="badge bg-success">可用</span>
                                    {% elif phone.status == 'in_use' and phone.current_owner_id != phone.original_owner_id %}
                                        <span class="badge bg-primary">使用中</span>
                                    {% elif phone.status == 'in_use' and phone.current_owner_id == phone.original_owner_id %}
                                        <span class="badge bg-success">可用</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ phone.status }}</span>
                                    {% endif %}
                                    <div class="small text-muted mt-1">
                                        {{ phone.created_at.strftime('%m-%d') if phone.created_at else '' }}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-phone display-4 text-muted"></i>
                        <p class="text-muted mt-3">暂无手机数据</p>
                        <a href="{{ url_for('add_phone') }}" class="btn btn-primary">
                            <i class="bi bi-plus me-2"></i>添加第一台手机
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 逾期借用提醒 -->
{% if overdue_loans %}
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-warning d-flex align-items-center">
            <i class="bi bi-exclamation-triangle-fill me-3 fs-4"></i>
            <div class="flex-grow-1">
                <h5 class="alert-heading mb-2">逾期借用提醒</h5>
                <p class="mb-2">有 {{ overdue_loans|length }} 条借用记录已逾期，请及时处理。</p>
                <a href="{{ url_for('loans') }}?filter=overdue" class="btn btn-warning btn-sm">
                    <i class="bi bi-eye me-2"></i>查看逾期记录
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    initStatusBars();
    
    // 自动刷新时间
    setInterval(updateLastUpdateTime, 60000); // 每分钟更新一次时间显示
});

function initStatusBars() {
    // 延迟启动动画，让页面完全加载
    setTimeout(() => {
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const targetWidth = bar.getAttribute('data-width');
            bar.style.width = targetWidth + '%';
        });
    }, 300);
}

function refreshStatusBars() {
    // 重新加载页面数据
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            // 更新统计卡片
            document.getElementById('totalPhones').textContent = data['总手机数'];
            document.getElementById('totalUsers').textContent = data['总用户数'];
            document.getElementById('activeLoans').textContent = data['活跃借用记录'];
            document.getElementById('overdueLoans').textContent = data['逾期借用记录'];
            
            // 更新进度条
            updateProgressBars(data['手机状态分布'], data['总手机数']);
            
            // 显示刷新成功提示
            showAlert('success', '数据已刷新');
            updateLastUpdateTime();
        })
        .catch(error => {
            console.error('刷新失败:', error);
            showAlert('danger', '刷新失败，请重试');
        });
}

function updateProgressBars(statusData, totalPhones) {
    const statusContainer = document.getElementById('statusBars');
    if (!statusContainer || !statusData) return;
    
    // 状态名称映射
    const statusNames = {
        'available': '可用',
        'in_use': '使用中',
        'maintenance': '维护中',
        'retired': '已退役'
    };
    
    // 清空现有内容
    statusContainer.innerHTML = '';
    
    // 重新生成进度条
    Object.entries(statusData).forEach(([status, count]) => {
        const percentage = totalPhones > 0 ? (count / totalPhones * 100) : 0;
        const statusName = statusNames[status] || status;
        
        const statusItem = document.createElement('div');
        statusItem.className = 'mb-4';
        statusItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div class="d-flex align-items-center">
                    <div class="status-dot status-${status}"></div>
                    <span class="fw-medium">${statusName}</span>
                </div>
                <div class="text-end">
                    <span class="fw-bold">${count}</span>
                    <small class="text-muted ms-1">(${percentage.toFixed(1)}%)</small>
                </div>
            </div>
            <div class="progress" style="height: 8px;">
                <div class="progress-bar bg-{% if status == 'available' %}success{% elif status == 'in_use' %}primary{% elif status == 'maintenance' %}warning{% else %}secondary{% endif %}" 
                     role="progressbar" 
                     style="width: 0%" 
                     data-width="${percentage}">
                </div>
            </div>
        `;
        statusContainer.appendChild(statusItem);
    });
    
    // 重新启动动画
    setTimeout(() => {
        const progressBars = statusContainer.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const targetWidth = bar.getAttribute('data-width');
            bar.style.width = targetWidth + '%';
        });
    }, 100);
}

function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.getFullYear() + '-' + 
                      String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                      String(now.getDate()).padStart(2, '0') + ' ' +
                      String(now.getHours()).padStart(2, '0') + ':' + 
                      String(now.getMinutes()).padStart(2, '0') + ':' + 
                      String(now.getSeconds()).padStart(2, '0');
    
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement) {
        lastUpdateElement.textContent = timeString;
    }
}
</script>

<style>
/* 状态指示器 */
.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.status-available {
    background-color: #198754;
    box-shadow: 0 0 0 2px rgba(25, 135, 84, 0.2);
}

.status-dot.status-in_use {
    background-color: #0d6efd;
    box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.2);
}

.status-dot.status-maintenance {
    background-color: #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

.status-dot.status-retired {
    background-color: #6c757d;
    box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.2);
}

/* 现代化进度条 */
.progress-modern {
    background-color: #f8f9fa;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.progress-bar {
    transition: width 1.2s ease-in-out;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

.progress-bar-available {
    background: linear-gradient(45deg, #198754, #20c997);
}

.progress-bar-in_use {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
}

.progress-bar-maintenance {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.progress-bar-retired {
    background: linear-gradient(45deg, #6c757d, #495057);
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 状态项悬停效果 */
.status-item {
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 8px;
}

.status-item:hover {
    background-color: rgba(0,0,0,0.02);
    transform: translateX(5px);
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 25px rgba(0,0,0,0.15) !important;
}

.stat-card {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.2) !important;
}

.stat-card:hover .bi-arrow-right {
    transform: translateX(3px);
    color: var(--bs-primary) !important;
}

.stat-card .bi-arrow-right {
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.list-group-item {
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    border-left-color: var(--bs-primary);
    background-color: rgba(13, 110, 253, 0.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .btn.py-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
    
    #statusChart {
        max-width: 250px;
        margin: 0 auto;
    }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,0,0,.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
</style>
{% endblock %}
