<!-- 手机列表AJAX模板片段 -->
{% if phones %}
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>IMEI</th>
                    <th>品牌型号</th>
                    <th>颜色</th>
                    <th>所属项目</th>
                    <th>状态</th>
                    <th>当前持有者</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for phone in phones %}
                    {% set current_owner = none %}
                    {% for user in users %}
                        {% if user.id == phone.current_owner_id %}
                            {% set current_owner = user %}
                        {% endif %}
                    {% endfor %}
                    <tr>
                        <td>
                            <code>{{ phone.imei }}</code>
                        </td>
                        <td>
                            <strong>{{ phone.brand }} {{ phone.model }}</strong>
                        </td>
                        <td>{{ phone.color }}</td>
                        <td>{{ phone.project }}</td>
                        <td>
                            {% if phone.status == 'available' %}
                                <span class="badge bg-success">可用</span>
                            {% elif phone.status == 'in_use' and phone.current_owner_id != phone.original_owner_id %}
                                <span class="badge bg-primary">使用中</span>
                            {% elif phone.status == 'in_use' and phone.current_owner_id == phone.original_owner_id %}
                                <span class="badge bg-success">可用</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ phone.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if current_owner %}
                                {{ current_owner.name }}
                                <small class="text-muted">({{ current_owner.department }})</small>
                            {% else %}
                                <span class="text-muted">未分配</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('phone_detail', imei=phone.imei) }}" 
                                   class="btn btn-sm btn-outline-info" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ url_for('phone_trace', imei=phone.imei) }}" 
                                   class="btn btn-sm btn-outline-success" title="追溯">
                                    <i class="bi bi-diagram-3"></i>
                                </a>
                                <a href="{{ url_for('edit_phone', imei=phone.imei) }}" 
                                   class="btn btn-sm btn-outline-warning" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        title="删除"
                                        onclick="deletePhone('{{ phone.imei }}', '{{ phone.brand }} {{ phone.model }}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="bi bi-phone display-1 text-muted"></i>
        <h4 class="mt-3 text-muted">
            {% if search_params.keyword or search_params.status or search_params.user_id %}
                未找到匹配的手机
            {% else %}
                暂无手机数据
            {% endif %}
        </h4>
        <p class="text-muted">
            {% if search_params.keyword or search_params.status or search_params.user_id %}
                请尝试调整搜索条件
            {% else %}
                点击上方按钮添加第一台手机
            {% endif %}
        </p>
        {% if not (search_params.keyword or search_params.status or search_params.user_id) %}
            <a href="{{ url_for('add_phone') }}" class="btn btn-primary">
                <i class="bi bi-phone-vibrate"></i> 添加手机
            </a>
        {% endif %}
    </div>
{% endif %}
